const {Server} = require('socket.io');

module.exports = {
  name: 'socket',
  settings: {
    port: 3001,
  },
  dependencies: ['settings'],


  async started() {

    // Khởi tạo Socket.IO server
    this.io = new Server(this.settings.port, {
      transports: ['websocket'],
      path: '/socket',
    });

    // Handle connections based on namespace/path
    this.io.of('chat').on('connection', async (socket) => {
      this.broker.emit('chatAi', socket);
    });
  },

  stopped() {
    if (this.io) {
      console.log('Shutting down WebSocket server...');
      this.io.close();
    }
  },
};
