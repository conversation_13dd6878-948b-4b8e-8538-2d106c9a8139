const ApiGateway = require('moleculer-web');
const i18next = require('i18next');
const backend = require('i18next-fs-backend');
const {enTranslation} = require('../locales/en/translation.js');
const {viTranslation} = require('../locales/vi/translation.js');
const _ = require('lodash');
const fs = require('fs');
const {MoleculerClientError} = require('moleculer').Errors;

const resources = {
  en: {translation: enTranslation},
  vi: {translation: viTranslation},
};

const SSE_RETRY_TIMEOUT = 15000; // 15 seconds
const SSE_HEADERS = {
  Connection: 'keep-alive',
  'Content-Type': 'text/event-stream',
  'Cache-Control': 'no-cache',
};

i18next
  .use(backend)
  .init({
    fallbackLng: 'en',
    preload: ['en', 'vi'],
    resources: resources,
    ns: ['translation'],
    backend: {
      loadPath: 'locales/{{lng}}/{{ns}}.js',
    },
  })
  .then(() => console.log('i18next initialized'));

module.exports = {
  name: 'api',
  mixins: [ApiGateway],

  settings: {
    port: process.env.PORT || 3000,
    ip: '0.0.0.0',
    use: [],
    routes: [
      {
        path: '/api',
        whitelist: ['**'],
        use: [],
        mergeParams: true,
        authentication: true,
        authorization: true,
        autoAliases: true,

        aliases: {
          'GET '(request, response) {
            response.writeHead(404);
            response.end();
          },
        },

        callingOptions: {},

        bodyParsers: {
          json: {
            strict: false,
            limit: '10MB',
          },
          urlencoded: {
            extended: true,
            limit: '10MB',
          },
        },

        mappingPolicy: 'all',
        logging: true,

        onBeforeCall(ctx, route, req, res) {
          const lang = req.headers.i18nextlng || 'vi';
          if (req.query?.token) {
            req.headers.authorization = `Bearer ${req.query?.token}`;
            delete req.query.token;
          }
          if (req.$action.name === 'blockchain.create') {
            ctx.meta.token = req.headers.authorization;
          }
          i18next.changeLanguage(lang);
          ctx.meta.lang = lang;
          ctx.meta.client_ip = req.headers['x-forwarded-for'] || req.connection.remoteAddress || req.socket.remoteAddress || req.connection.socket.remoteAddress;
          ctx.meta.user_agent = req.headers['user-agent'];
        },
        onError(req, res, err) {
          res.setHeader('Content-Type', 'application/json; charset=utf-8');
          res.writeHead(err.statusCode || err.code || 500);
          res.end(JSON.stringify({
            success: false,
            message: err.message || err,
            code: err.code || 500,
            ...(err.data && {data: err.data}),
          }));
        },
      },
      {
        path: '/upload',

        authentication: true,
        authorization: true,

        bodyParsers: {
          json: true,
          urlencoded: false,
        },

        aliases: {
          'POST /file': 'multipart:files.upload',
          'POST /google-account': 'multipart:googleaccounts.upload',
        },

        onBeforeCall(ctx, route, req, res) {
          if (req.headers.range) {
            ctx.meta.range = req.headers.range;
          }
        },

        busboyConfig: {
          limits: {
            files: 1,
          },
        },

        callOptions: {
          meta: {},
        },
        mappingPolicy: 'restrict',
      },
      {
        path: '/api/responses/:responseId',
        authentication: true,
        aliases: {
          'GET stream-content'(request, response) {
            const {responseId} = request.$params;
            response.writeHead(200, SSE_HEADERS);
            response.$service.addSSEListener(response, responseId);
            // response.$ctx.emit(`listenResponse`, { responseId });
          },
        },
      },
      {
        path: '/api/files/:fileId',
        authentication: true,
        aliases: {
          async 'GET stream-media'(request, response) {
            try {
              const {fileId} = request.$params;
              const videoPath = await response.$ctx.call(`files.filePath`, {id: fileId});
              const videoSize = fs.statSync(videoPath).size;
              let range = request.headers.range;
              if (!range) {
                const head = {
                  'Content-Type': 'video/mp4',
                  'Content-Length': videoSize,
                };
                response.writeHead(200, head);
                fs.createReadStream(videoPath).pipe(response);
              } else {
                const CHUNK_SIZE = 10 ** 6;
                const start = Number(range.replace(/\D/g, ''));
                const end = Math.min(start + CHUNK_SIZE, videoSize - 1);
                const contentLength = end - start + 1;
                const headers = {
                  'Content-Range': `bytes ${start}-${end}/${videoSize}`,
                  'Accept-Ranges': 'bytes',
                  'Content-Length': contentLength,
                  'Content-Type': 'video/mp4',
                };
                response.writeHead(206, headers);
                const readStream = fs.createReadStream(videoPath, {start, end});
                readStream.pipe(response);
              }
            } catch (e) {
              console.log(e);
              response.writeHead(404);
              response.end();
            }
          },
        },
      },
      // {
      //   path: '/',
      //   whitelist: [],
      //   aliases: {
      //     'GET '(request, response) {
      //       response.writeHead(404);
      //       response.end();
      //     },
      //   },
      // },
    ],

    log4XXResponses: false,
    logRequestParams: null,
    logResponseData: null,
    assets: {
      folder: 'public',
      options: {},
    },
    // Global error handler
    onError(req, res, err) {
      res.setHeader('Content-Type', 'text/plain');
      res.writeHead(err.statusCode || err.code || 501);
      res.end(JSON.stringify({
        success: false,
        code: err.code,
        message: err.message,
      }));
    },
  },

  methods: {
    async authenticate(ctx, route, req) {
      let accessToken, refreshToken, resetPasswordToken, activationToken;
      if (req.headers.cookie) {
        const cookieHeaders = req.headers.cookie.split(';');
        cookieHeaders.forEach((cookieItem) => {
          const [type, token] = cookieItem.trim().split('=');
          if (type === 'accessToken') accessToken = token;
          if (type === 'refreshToken') refreshToken = token;
        });
      }

      const urlResetPassword = req.originalUrl.includes('reset-password');
      const urlActivation = req.originalUrl.includes('active-account');

      if (req.headers.authorization) {
        let [type, token] = req.headers.authorization.split(' ');
        if (type === 'Token' || type === 'Bearer') {
          if (urlResetPassword) resetPasswordToken = token;
          if (urlActivation) activationToken = token;
        }
      }

      ctx.meta.refreshToken = refreshToken;
      let user;

      if (activationToken) {
        user = await ctx.call('users.resolveActivationToken', {activationToken});
        if (user) {
          ctx.meta.activationToken = activationToken;
          ctx.meta.userID = user._id;
          return _.pick(user, ['_id', 'email', 'isSystemAdmin', 'fullName']);
        } else {
          throw new ApiGateway.Errors.UnAuthorizedError('NO_RIGHTS');
        }
      }

      if (resetPasswordToken) {
        user = await ctx.call('users.resolveResetPasswordToken', {resetPasswordToken});
        if (user) {
          ctx.meta.resetPasswordToken = resetPasswordToken;
          ctx.meta.userID = user._id;
          return _.pick(user, ['_id', 'email', 'isSystemAdmin', 'fullName']);
        } else {
          throw new ApiGateway.Errors.UnAuthorizedError('NO_RIGHTS');
        }
      }

      if (accessToken) {
        try {
          user = await ctx.call('users.resolveToken', {accessToken, refreshToken});
          if (user) {
            ctx.meta.accessToken = accessToken;
            return _.pick(user, ['_id', 'email', 'isSystemAdmin', 'fullName']);
          }
        } catch (err) {
          // Ignored because we continue processing if the user doesn't exist
        }
      }
    },

    async authorize(ctx, route, req) {
      // Get the authenticated user.
      const user = ctx.meta.user;
      req.user = user;
      //Check and change language
      let lang = req.headers.i18nextlng;
      i18next.changeLanguage(lang);
      // It checks the `auth` property in the action schema.
      if (req.$action.auth === 'required' && !user) {
        throw new ApiGateway.Errors.UnAuthorizedError('NO_RIGHTS');
      }

      // Check admin role
      if (req.$action.admin === true && !user?.isSystemAdmin) {
        throw new MoleculerClientError(i18next.t('error_permission_denied'), 403, 'NO_PERMISSION');
      }
    },

    addSSEListener(stream, event) {
      if (!stream.write)
        throw new MoleculerError('Only writable can listen to SSE.');

      const listeners = this.sseListeners.get(event) || new Set();
      listeners.add(stream);
      this.sseListeners.set(event, listeners);
      this.sseIds.set(stream, 0);

      // Biến để lưu trữ ID của timeout
      let timeoutId = null;

      // Hàm tạo hoặc reset timeout
      const resetTimeout = () => {
        // Xóa timeout hiện tại nếu có
        if (timeoutId) clearTimeout(timeoutId);

        // Tạo timeout mới
        timeoutId = setTimeout(() => {
          if (this.sseIds.has(stream)) {
            stream.end('event: timeout\ndata: {"message":"Connection timeout"}\n\n');
            listeners.delete(stream);
            this.sseIds.delete(stream);
          }
        }, this.sseRetry * 2);
      };

      // Thiết lập timeout ban đầu
      resetTimeout();

      // Tạo heartbeat và đảm bảo nó reset timeout
      const heartbeatInterval = setInterval(() => {
        if (this.sseIds.has(stream)) {
          stream.write('event: heartbeat\ndata: {}\n\n');
          resetTimeout(); // Reset timeout sau mỗi lần heartbeat
        } else {
          clearInterval(heartbeatInterval);
        }
      }, Math.floor(this.sseRetry / 3));

      // Override phương thức write để reset timeout mỗi khi có dữ liệu được gửi
      const originalWrite = stream.write;
      stream.write = function(...args) {
        resetTimeout();
        return originalWrite.apply(this, args);
      };

      stream.on('close', () => {
        clearTimeout(timeoutId);
        clearInterval(heartbeatInterval);
        this.sseIds.delete(stream);
        listeners.delete(stream);
      });

      stream.on('error', () => {
        clearTimeout(timeoutId);
        clearInterval(heartbeatInterval);
        this.sseIds.delete(stream);
        listeners.delete(stream);
        stream.end();
      });
    },
    handleSSE(context) {
      const {eventName, params} = context;
      const event = eventName.replace('sse.', '');
      if (!this.sseListeners.has(event)) return;
      const listeners = this.sseListeners.get(event);
      const isEnd = params.state === 'done' || params.state === 'error';
      for (const stream of listeners.values()) {
        const id = this.sseIds.get(stream) || 0;
        const message = this.createSSEMessage(params, event, id);
        stream.write(message);
        this.sseIds.set(stream, id + 1);
        if (isEnd) {
          stream.end();
          listeners.delete(stream);
          this.sseIds.delete(stream);
        }
      }
    },
    createSSEMessage(data, event, id) {
      return `event: ${event}\ndata: ${JSON.stringify(
        data,
      )}\nid: ${id}\nretry: ${this.sseRetry}\n\n`;
    },
  },
  started() {
    this.sseListeners = new Map();
    this.sseIds = new WeakMap();
    this.sseRetry = SSE_RETRY_TIMEOUT;
  },
  stopped() {
    for (const listeners of this.sseListeners.values()) {
      for (const stream of listeners.values()) {
        if (stream.end) stream.end();
        listeners.delete(stream);
        this.sseIds.delete(stream);
      }
    }
  },
  events: {
    'sse.*'(context) {
      this.handleSSE(context);
    },
  },
};
