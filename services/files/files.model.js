const mongoose = require('mongoose');
const {Schema} = require('mongoose');
const {FILE} = require('../../constants/dbCollections');
const mongoosePaginate = require('mongoose-paginate-v2');

const schema = new Schema({
  name: {type: String, required: true, validate: /\S+/},
  displayName: {type: String, validate: /\S+/},
  mimetype: {type: String},
  size: {type: String},
  storageType: {type: String},
  storageLocation: {type: String},
  url: {type: String},
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});
schema.plugin(mongoosePaginate);
module.exports = mongoose.model(FILE, schema, FILE);

