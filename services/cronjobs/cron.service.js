const CronJob = require('moleculer-cron');
const {CronTime} = require('cron');

/** @type {ServiceSchema} */
module.exports = {
  name: 'cronjobs',
  mixins: [CronJob],
  dependencies: ['settings'],
  settings: {
    runOnInit: true,
    cronJobs: [
      // {
      //   // Craw Ophim website every day at 0:35 AM
      //   name: 'crawMovieDataOphim',
      //   cronTime: '35 0 * * *',
      //   async onTick() {
      //     try {
      //       this.logger.info(`Job craw Ophim started at ${new Date().toISOString()}`);
      //       await this.broker.emit('movie.craw', {domain: 'ophim'});
      //     } catch (error) {
      //       this.logger.info(`Job craw OPhim started with error: ${error.message}`);
      //     }
      //   },
      // },
      // {
      //   // Craw NguonC website every day at 1:35 AM
      //   name: 'crawMovieDataNguonC',
      //   cronTime: '35 1 * * *',
      //   async onTick() {
      //     try {
      //       this.logger.info(`Job craw NguonC started at ${new Date().toISOString()}`);
      //       await this.broker.emit('movieCraw', {domain: 'nguonc'});
      //     } catch (error) {
      //       this.logger.info(`Job craw NguonC started with error: ${error.message}`);
      //     }
      //   },
      // },
      // {
      //   // Craw KkPhim website every day at 2:35 AM
      //   name: 'crawMovieDataKkPhim',
      //   cronTime: '35 2 * * *',
      //   // cronTime: '*/1 * * * *',
      //   async onTick() {
      //     try {
      //       this.logger.info(`Job craw KkPhim started at ${new Date().toISOString()}`);
      //       await this.broker.emit('movie.craw', {domain: 'kkphim'});
      //     } catch (error) {
      //       this.logger.info(`Job craw KkPhim started with error: ${error.message}`);
      //     }
      //   },
      // },
      // {
      //   // Update episode error every day at 3:35 AM
      //   name: 'UpdateEpisodeError',
      //   cronTime: '35 12 * * *',
      //   async onTick() {
      //     try {
      //       this.logger.info(`Job update episodes error started at ${new Date().toISOString()}`);
      //       await this.broker.emit('JobUpdateEpisodeError');
      //     } catch (error) {
      //       this.logger.info(`Job update episodes error started with error: ${error.message}`);
      //     }
      //   },
      // },
      // {
      //   // Update episode error every day at 3:35 AM
      //   name: 'UpdateMovie',
      //   cronTime: '0 0 * * *',
      //   async onTick() {
      //     try {
      //       this.logger.info(`Job update film started at ${new Date().toISOString()}`);
      //       await this.broker.emit('jobUpdateFilm');
      //     } catch (error) {
      //       this.logger.info(`Job update film started with error: ${error.message}`);
      //     }
      //   },
      // },
    ],
  },

  actions: {},
  methods: {},
  events: {
    // settingUpdate: {
    //   async handler(ctx) {
    //     const data = ctx.params
    //     if (data.cronTime) {
    //       this.getJob('clear-storage').setTime(new CronTime(data.cronTime));
    //     }
    //   }
    // }
  },
  created() {
  },

  async started() {
    // try {
    //   const cronTime = await this.broker.call('settings.getCronTime', {}) || '0 1 * * *';
    //   this.getJob('clear-storage').setTime(new CronTime(cronTime));
    // } catch (error) {
    //   console.error('Error:', error);
    // }
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },
};
