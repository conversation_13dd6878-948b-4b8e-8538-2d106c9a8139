const {getConfig} = require('../../../config/config');
const config = getConfig(process.env.NODE_ENV);
const fs = require('fs');
const path = require('path');
const Handlebars = require('handlebars');

function getTemplate(templateName) {
  const fullPath = path.join(__dirname, 'template', templateName);
  return fs.readFileSync(fullPath).toString();
}

function replacement(templateString, context) {
  const template = Handlebars.compile(templateString);
  return template(context);
}

const replaceConstains = {
  supportEmail: config.supportEmail,
  websiteName: config.websiteName,
};

exports.createRegisterEmail = (userInfo, activateLink) => {
  const replacements = {
    ...replaceConstains,
    activateLink: activateLink,
    account: userInfo.email,
    userFullname: userInfo.fullName,
    trialTime: '1 năm',
    hasPassword: !!userInfo.password,
  };
  return replacement(getTemplate('register.html'), replacements);
};

exports.createActiveAccountEmail = (userInfo, activateLink) => {
  const replacements = {
    ...replaceConstains,
    activateLink: activateLink,
    userFullname: userInfo.fullName,
  };
  return replacement(getTemplate('active_account.html'), replacements);
};

exports.createChangePasswordEmail = (userInfo, changeTime) => {
  const replacements = {
    ...replaceConstains,
    userFullname: userInfo.fullName,
    changeTime: changeTime,
  };
  return replacement(getTemplate('change_password.html'), replacements);
};

exports.createForgotPasswordEmail = (userInfo, resetPasswordLink, expirationTime) => {
  const replacements = {
    ...replaceConstains,
    name: userInfo.email,
    userFullname: userInfo.fullName,
    resetPasswordLink: resetPasswordLink,
    expirationTime: expirationTime,
  };
  return replacement(getTemplate('forgot_password.html'), replacements);
};
