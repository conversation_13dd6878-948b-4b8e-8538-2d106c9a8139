<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Security Alert</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #dc3545; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .alert { background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 4px; margin: 20px 0; color: #721c24; }
        .button { display: inline-block; padding: 12px 24px; background: #dc3545; color: white; text-decoration: none; border-radius: 4px; margin: 20px 0; }
        .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚨 Security Alert</h1>
        </div>
        <div class="content">
            <h2>Security Alert for Your Account</h2>
            <p>Hello {{userFullname}},</p>
            <div class="alert">
                <strong>Alert Type:</strong> {{alertType}}<br>
                <strong>Time:</strong> {{alertTime}}<br>
                <strong>Details:</strong> {{details}}
            </div>
            <p><strong>What happened:</strong></p>
            <p>We detected unusual activity on your account that requires your attention.</p>
            <p><strong>Recommended actions:</strong></p>
            <ul>
                <li>Review your recent account activity</li>
                <li>Change your password if you suspect unauthorized access</li>
                <li>Enable two-factor authentication for added security</li>
                <li>Check your active sessions and revoke any suspicious ones</li>
            </ul>
            <p><strong>If this was you:</strong> No action is needed, but we recommend reviewing your security settings.</p>
            <p><strong>If this wasn't you:</strong> Please secure your account immediately and contact our support team.</p>
            <a href="{{websiteName}}/security" class="button">Review Security Settings</a>
        </div>
        <div class="footer">
            <p>This email was sent by {{websiteName}}</p>
            <p>If you need help, contact us at {{supportEmail}}</p>
        </div>
    </div>
</body>
</html>
