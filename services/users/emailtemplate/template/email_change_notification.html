<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Email Address Changed</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #28a745; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .alert { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 4px; margin: 20px 0; }
        .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{websiteName}}</h1>
        </div>
        <div class="content">
            <h2>Email Address Successfully Changed</h2>
            <p>Hello {{userFullname}},</p>
            <p>This is to confirm that your email address has been successfully changed.</p>
            <div class="alert">
                <strong>Change Details:</strong><br>
                Previous Email: {{oldEmail}}<br>
                New Email: {{newEmail}}<br>
                Changed On: {{changeTime}}
            </div>
            <p><strong>What this means:</strong></p>
            <ul>
                <li>All future communications will be sent to your new email address</li>
                <li>You will need to use your new email address for login</li>
                <li>Your account security and settings remain unchanged</li>
            </ul>
            <p><strong>Security Notice:</strong> If you did not make this change, please contact our support team immediately at {{supportEmail}}.</p>
        </div>
        <div class="footer">
            <p>This email was sent by {{websiteName}}</p>
            <p>If you need help, contact us at {{supportEmail}}</p>
        </div>
    </div>
</body>
</html>
