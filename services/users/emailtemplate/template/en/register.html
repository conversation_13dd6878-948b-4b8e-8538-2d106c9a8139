<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <style>
    body {
      font-family: 'Arial', sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 20px;
      background-color: #f4f4f4;
    }

    .container {
      max-width: 600px;
      margin: 0 auto;
      background-color: #fff;
      padding: 30px;
      border-radius: 8px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }

    h1, h2, h3 {
      color: #007bff;
    }

    p {
      margin-bottom: 15px;
    }

    ul, ol {
      margin-bottom: 15px;
      padding-left: 20px;
    }

    li {
      margin-bottom: 8px;
    }

    strong {
      font-weight: bold;
    }

    a {
      color: #007bff;
      text-decoration: none;
    }

    a:hover {
      text-decoration: underline;
    }

    .greeting {
      font-size: 1.2em;
      margin-bottom: 20px;
    }

    .account-info {
      margin-bottom: 20px;
      border: 1px solid #ddd;
      padding: 15px;
      border-radius: 4px;
      background-color: #f9f9f9;
    }

    .account-info ul {
      padding-left: 20px;
      margin-bottom: 0;
    }

    .button {
      display: inline-block;
      background-color: #28a745;
      color: white;
      padding: 10px 20px;
      text-decoration: none;
      border-radius: 5px;
      margin-top: 15px;
    }

    .button:hover {
      background-color: #218838;
    }

    .signature {
      margin-top: 30px;
      font-style: italic;
      color: #777;
    }
  </style>
</head>
<body>
<div class="container">
  <p class="greeting">Dear <strong>{{userFullname}}</strong>,</p>

  <p>We are delighted to welcome you to <strong>{{websiteName}}</strong>! Your account has been successfully created. Welcome to our community.</p>

  <h2>Account Information:</h2>
  <div class="account-info">
    <ul>
      <li><strong>Account Name:</strong> {{account}}</li>
      {{#if trialTime}}
      <li><strong>Trial Period:</strong> {{trialTime}}</li>
      {{/if}}
    </ul>
  </div>

  <h2>Get Started:</h2>
  <p>To start using your account, please click the activation link below:</p>
  <p><a class="button" href="{{activateLink}}">Activate Your Account</a></p>

  {{#if hasPassword}}
  <p>You can log in using your account name and chosen password.</p>
  {{else}}
  <p>Since you haven't set a password yet, please visit the activation link above. After activation, you will be able to set a password for your account.</p>
  {{/if}}

  <h2>Explore {{websiteName}}:</h2>
  <p>{{welcomeMessage}}</p>

  <h2>Support:</h2>
  <p>If you have any questions or need support, please feel free to contact us via email: <a
    href="mailto:{{supportEmail}}">{{supportEmail}}</a>.</p>

  <p class="signature">Best regards,<br>
    The {{websiteName}} Team</p>
</div>
</body>
</html>
