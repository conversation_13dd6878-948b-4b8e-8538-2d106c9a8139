const Model = require('./sessions.model');
const DbMongoose = require('../../../mixins/dbMongo.mixin');
const BaseService = require('../../../mixins/baseService.mixin');
const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');
const AdminService = require('../../../mixins/adminService.mixin');
const {MoleculerClientError} = require('moleculer').Errors;
const {generateSecureToken} = require('../../../helpers/usersHelper');

module.exports = {
  name: 'sessions',
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon, AdminService],

  settings: {
    fields: ['_id', 'ip', 'type', 'isActive', 'expiresDate', 'userId'],
    populates: {
      userId: 'users.get',
    },
    populateOptions: ['userId'],
  },

  actions: {
    list: {admin: false},
    me: {
      rest: 'GET /me',
      auth: 'required',
      admin: false,
      async handler(ctx) {
        try {
          const {refreshToken, user} = ctx.meta;
          const {time} = ctx.params;

          const query = {
            userId: user._id,
            refreshToken: {$ne: refreshToken},
            isDeleted: false,
          };

          if (time) {
            query.createdAt = this.extractQueryTime(ctx.params);
          }
          const paramsList = this.extractParamsList(ctx.params);
          const params = {
            ...paramsList,
            // searchFields: "email,fullName",
            query: JSON.stringify(query),
          };
          return ctx.call('sessions.list', params);
        } catch (e) {
          console.log(e);
        }
      },
    },
    remove: {
      rest: 'DELETE /:id',
      auth: 'required',
      admin: false,
      async handler(ctx) {
        const {id} = ctx.params;
        const {refreshToken, user} = ctx.meta.refreshToken;

        const session = await this.adapter.findOne({
          _id: id,
          userId: user._id,
          refreshToken: {$ne: refreshToken},
        });

        if (!session) {
          throw new MoleculerClientError('Session not found', 404);
        }

        await ctx.call('refreshtokens.updateMany', {
          query: {refreshToken: session.refreshToken},
          update: {isDeleted: true},
        });
        return this.adapter.updateById(id, {isDeleted: false}, {new: true});
      },
    },
    deleteMany: {
      rest: 'DELETE /deleteMany',
      auth: 'required',
      admin: false,
      async handler(ctx) {
        const {refreshToken, user} = ctx.meta;
        const query = {
          userId: user._id,
          refreshToken: {$ne: refreshToken},
        };

        await ctx.call('refreshtokens.updateMany', {query, update: {isDeleted: true}});
        await this.adapter.updateMany(query, {isDeleted: true});

        // // Emit sessions revoked event
        // this.broker.emit('user.sessions.revoked', {
        //   userId,
        //   count: sessions.length,
        //   keepCurrent,
        //   ctx,
        // });

        return {success: true};
      },
    },
    removeCurrent: {
      rest: 'DELETE /current',
      auth: 'required',
      admin: false,
      async handler(ctx) {
        const {refreshToken, user} = ctx.meta;
        await ctx.call('refreshtokens.updateMany', {
          query: {userId: user._id, refreshToken: refreshToken},
          update: {isDeleted: true},
        });
        return this.adapter.updateMany({refreshToken}, {isDeleted: true});
      },
    },
  },

  methods: {},
  events: {
    async 'user.login'(ctx) {
      const {user, refreshToken} = ctx.params;
      const {user_agent: userAgent, client_ip: ip} = ctx.meta;

      let type;
      if (userAgent.includes('Mobile')) {
        type = 'Mobile';
      } else if (userAgent.includes('Tablet') || userAgent.includes('iPad')) {
        type = 'Tablet';
      } else if (userAgent.includes('Chrome') || userAgent.includes('Firefox') ||
        userAgent.includes('Safari') || userAgent.includes('Edge')) {
        type = 'Web';
      } else {
        type = 'Other';
      }

      return await this.adapter.insert({userId: user._id, refreshToken, type, ip});
    },
  },

  started() {
  },
  stopped() {
  },
};
