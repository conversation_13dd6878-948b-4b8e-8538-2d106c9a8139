const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const {USER_SESSION, USER} = require('../../../constants/dbCollections');
const {Schema, ObjectId} = mongoose;

const schema = new Schema({
  userId: {type: ObjectId, ref: USER, required: true},
  refreshToken: {type: String, required: true},
  ip: {type: String},
  type: {type: String},
  expiresDate: {type: Date, required: true, default: () => new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)},
  isDeleted: {type: Boolean, default: false},
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

// TTL index to automatically delete expired sessions
schema.index({expiresDate: 1}, {expireAfterSeconds: 0});

schema.plugin(mongoosePaginate);

module.exports = mongoose.model(USER_SESSION, schema, USER_SESSION);
