const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const {USER} = require('../../constants/dbCollections');
const {encryptPassword} = require('../../helpers/usersHelper');
const {Schema} = mongoose;

const schema = new Schema({
  fullName: {type: String, trim: true, default: 'New Account'},
  email: {type: String, trim: true, unique: true, index: true, lowercase: true, required: 'Please fill in an email'},
  username: {type: String, trim: true, index: true, lowercase: true}, // Thêm username cho login
  password: {type: String},
  isDeleted: {type: <PERSON>olean, default: false, select: false},
  isSystemAdmin: {type: Boolean, default: false},
  active: {type: Boolean, default: false},
  timesWrongPass: { type: Number, default: 0 },
  neverLogin: { type: <PERSON><PERSON><PERSON>, default: true },
  lastLogin: { type: Date },
  lastChangePassword: { type: Date, default: new Date() },
  totpSecret: {type: String},
  totpSetupCompleted: {type: Boolean, default: false},
  totpBackupCodes: [{type: String}],
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.pre('save', function(next) {
  let user = this;
  if (!user.isModified('password')) return next();
  user.password = encryptPassword(user.password);
  next();
});

schema.plugin(mongoosePaginate);

module.exports = mongoose.model(USER, schema, USER);
