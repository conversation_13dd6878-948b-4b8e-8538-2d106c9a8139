const DbMongoose = require('../../../mixins/dbMongo.mixin');
const Model = require('./apikeys.model');
const BaseService = require('../../../mixins/baseService.mixin');
const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');
const AdminService = require('../../../mixins/adminService.mixin');

module.exports = {
  name: 'apikeys',
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon],
  settings: {
    populates: {},
    populateOptions: [],
  },

  hooks: {
    after: {
      async list(ctx, res) {
        res.rows.forEach(r => {
          r.apiKey = this.convertKey(r.apiKey);
        });
        return res;
      },
      getAllWithoutPagination: 'convertAPIKeyForList',
      get: 'convertAPIKeyForObj',
    },
  },

  actions: {
    update: {
      async handler(ctx) {
        const {id, ...param} = ctx.params;
        const entity = {
          modelInterface: null,
          region: null,
          endpoint: null,
          apiVersion: null,
          deployment: null,
          awsAccessKey: null,
          awsSecretKey: null,
          awsRegion: null,
          ...param,
        };

        return this.adapter.updateById(id, entity);
      },
    },
    getOne: {
      async handler(ctx) {
        const {id} = ctx.params;
        const data = await this.adapter.findOne({_id: id});
        if (!data) {
          return;
        }
        return data;
      },
    },
  },
  methods: {
    convertAPIKeyForList(ctx, res) {
      return res.map(r => {
        const {apiKey} = r;
        r.apiKey = this.convertKey(apiKey);
        return r;
      });
    },
    convertAPIKeyForObj(ctx, res) {
      Object.values(res).forEach(r => {
        const {apiKey} = r;
        r.apiKey = this.convertKey(apiKey);
      });
      return res;
    },

    convertKey(key) {
      const length = key.length;
      const third = Math.floor(length / 3);
      if (third === 0) return key.replace(/./g, '*');

      const prefix = key.slice(0, third);
      const suffix = key.slice(-third);
      const middle = '*'.repeat(length - 2 * third);

      return `${prefix}${middle}${suffix}`;
    },
  },
  events: {},
  created() {
  },
  async started() {
  },
  async stopped() {
  },
  async afterConnected() {
  },
};
