const {API_KEY} = require('../../../constants/dbCollections');
const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');

const schema = new mongoose.Schema({
  apiKey: {type: String, required: true, unique: true},
  modelInterface: {type: String, required: true},
  // configKey: {type: mongoose.Schema.mixed},

  region: {type: String},
  endpoint: {type: String},

  // aws bedrock
  awsAccessKey: {type: String},
  awsSecretKey: {type: String},
  awsRegion: {type: String},

  // vertex ai
  location: {type: String},
  project: {type: String},
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);

module.exports = mongoose.model(API_KEY, schema, API_KEY);
