const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const {MODAL_AI, API_KEY} = require('../../../constants/dbCollections');

const {Schema} = mongoose;
const schema = new Schema({
  tokenUnit: {type: Number},
  unit: {type: String},
  modal: {type: String, required: true},
  maxTokens: {type: Number},
  priceInput: {type: Number},
  priceOutput: {type: Number},
  apiKeyId: {type: Schema.Types.ObjectId, ref: API_KEY, required: true},
  apiVersion: {type: String},
  deployment: {type: String},
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(MODAL_AI, schema, MODAL_AI);
