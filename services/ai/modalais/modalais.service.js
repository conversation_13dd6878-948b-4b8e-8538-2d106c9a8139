const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');
const Model = require('./modalais.model');
const DbMongoose = require('../../../mixins/dbMongo.mixin');
const BaseService = require('../../../mixins/baseService.mixin');
const AdminService = require('../../../mixins/adminService.mixin');
const {ObjectId} = require('mongoose').Types;

module.exports = {
  name: 'modalais',
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon],
  settings: {
    populates: {
      'apiKeyId': 'apikeys.get',
    },
    populateOptions: ['apiKeyId'],
  },
  hooks: {
    before: {
      getAllWithoutPagination: async (ctx) => {
        console.log('ctx.params', ctx.params);
        ctx.params.sort = '-createdAt';
      },
    },
  },

  actions: {
    findOne: {
      rest: 'GET /findOne',
      admin: true,
      async handler(ctx) {
        const {model} = ctx.params;
        const data = await this.adapter.findOne({model});
        if (!data) return;

        return data;
      },
    },
    getOneByModel: {
      rest: 'GET /getOneByModel',
      admin: true,
      async handler(ctx) {
        const {model} = ctx.params;
        if (!model) return;

        const data = await this.adapter.findOne({model});
        if (!data) return;

        const {apiVersion, deployment, apikeyId} = data;

        const {apiKeyId, maxTokens} = data;
        const APIKeyData = await ctx.call('apikeys.getOne', {id: data.apiKeyId});
        if (!APIKeyData) return;

        const {apiKey, modelInterface, endpoint} = APIKeyData;
        return {
          maxTokens,
          modelInterface,
          configKey: {
            apiKey,
            ...(apiVersion && {apiVersion}),
            ...(deployment && {deployment}),
            ...(endpoint && {endpoint}),
          },
        };
      },
    },
  },

  methods: {},
};
