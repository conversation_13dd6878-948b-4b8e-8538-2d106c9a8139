const fs = require('fs');
const {GoogleGenAI, Type} = require('@google/genai');
const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');

module.exports = {
  name: 'gemini',
  mixins: [FunctionsCommon],
  settings: {},
  hooks: {},

  dependencies: [],
  actions: {
    chatCompletion: {
      timeout: 5 * 60 * 1000,
      rest: 'POST /chatCompletion',
      admin: true,
      async handler(ctx) {
        try {
          const {messages, model, schema, responseFormat, responseId, temperature, maxTokens, configKey} = ctx.params;

          const [systemInstruction, contentMessage] = this.transformMessages(messages);
          const gemini = new GoogleGenAI(configKey);
          let body = {
            model: model,
            contents: [...contentMessage],
            temperature,
            config: {
              responseMimeType: 'text/plain',
              systemInstruction,
              maxOutputTokens: maxTokens,
            },
          };

          if (responseFormat === 'json_object') {
            body = {
              ...body,
              config: {
                ...(model.includes('2') && {thinkingConfig: {thinkingBudget: 1024}}),
                responseMimeType: 'application/json',
                responseSchema: this.transformSchema(schema),
              },
              temperature,
            };
          }
          const completion = await gemini.models.generateContent(body);
          this.broker.emit('llmGenerateCompleted', {
            id: responseId,
            completionTokens: completion.usageMetadata.candidatesTokenCount,
            promptTokens: completion.usageMetadata.promptTokenCount,
            totalTokens: completion.usageMetadata.totalTokenCount,
            model,
          });

          return responseFormat === 'json_object' ? JSON.parse(completion.candidates[0].content.parts[0].text)
            : completion.candidates[0].content.parts[0].text;
        } catch (err) {
          console.log(err);
          return err;
        }
      },
    },
    completion: {
      timeout: 5 * 60 * 1000,
      rest: 'POST /completion',
      admin: true,
      async handler(ctx) {
        const {model, maxTokens, prompt, configKey} = ctx.params;

        const transMessages = this.transformMessages(prompt);
        let body = {
          model: model,
          contents: [...transMessages],
          config: {
            responseMimeType: 'text/plain',
            maxOutputTokens: maxTokens,
          },
        };

        const client = new GoogleGenAI(configKey);
        const completion = await client.models.generateContent(body);
        return completion;
      },
    },
  },
  events: {},
  methods: {
    async checkSize(filePath) {
      try {
        let stats = fs.statSync(filePath);
        let fileSizeInBytes = stats.size;
        let fileSizeInMegabytes = fileSizeInBytes / (1024 * 1024);
        return fileSizeInMegabytes;
      } catch (error) {
        return error;
      }
    },
    transformSchema(schema) {
      if (typeof schema !== 'object' || schema === null) {
        return schema;
      }

      const mappedObject = Object.fromEntries(
        Object.entries(schema).map(([key, value]) => {
          const mappedValue = this.transformSchema(value);
          return [key, mappedValue];
        }),
      );

      if (mappedObject.hasOwnProperty('type') && typeof mappedObject.type === 'string') {
        mappedObject.type = mappedObject.type.toUpperCase();
      }

      return mappedObject;
    },
    transformMessages(messages) {
      const contentMessage = messages
        .filter(message => message.role !== 'system')
        .map(message => {
          message.role = message.role === 'assistant' ? 'model' : 'user';
          const newMessage = {...message};
          newMessage.parts = [{
            text: message.content,
          }];
          delete newMessage.content;
          return newMessage;
        });
      const systemInstruction = messages
        .filter(message => message.role === 'system')
        .map(message => ({text: message.content}));

      return [systemInstruction, contentMessage];
    },
  },
  created() {
  },
  started() {
  },
  stopped() {
  },
};
