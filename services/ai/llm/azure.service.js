const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');
const {AzureOpenAI} = require('openai');

module.exports = {
  name: 'azureopenai',

  mixins: [FunctionsCommon],
  settings: {},
  hooks: {},
  dependencies: [],
  actions: {
    chatCompletion: {
      timeout: 5 * 60 * 1000,
      rest: 'POST /chatCompletion',
      // admin: true,
      async handler(ctx) {
        try {
          let {messages, model, schema, responseFormat, responseId, temperature, maxTokens, configKey} = ctx.params;

          const client = new AzureOpenAI(configKey);
          let body = {
            model,
            messages,
            max_tokens: maxTokens,
            ...(responseFormat === 'json_object' && {
              tools: [{
                type: 'function',
                function: {name: 'show_response', description: 'Show the response', parameters: schema},
              }],
              tool_choice: {type: 'function', function: {name: 'show_response'}},
            }),
          };

          const completion = await client.chat.completions.create(body);
          const {completion_tokens, prompt_tokens, total_tokens} = completion.usage;
          this.broker.emit('llmGenerateCompleted', {
            id: responseId,
            completionTokens: completion_tokens,
            promptTokens: prompt_tokens,
            totalTokens: total_tokens,
            model,
          });

          return responseFormat === 'json_object'
            ? JSON.parse(completion.choices[0].message.tool_calls[0].function.arguments)
            : completion.choices[0].message.content;
        } catch (err) {
          console.log(err);
          return err;
        }
      },
    },
    completion: {
      timeout: 5 * 60 * 1000,
      rest: 'POST /completion',
      admin: true,
      async handler(ctx) {
        const {model, maxTokens, prompt, configKey} = ctx.params;

        let body = {
          model,
          prompt,
          max_tokens: maxTokens,
        };
        const client = new AzureOpenAI(configKey);
        const completion = await client.chat.completions.create(body);
        return completion.choices[0].message.content;
      },
    },
  },
  events: {},
  methods: {},
  created() {
  },
  started() {
  },
  stopped() {
  },
};
