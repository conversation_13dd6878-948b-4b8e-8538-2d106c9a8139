const fs = require('fs');
const {GoogleGenAI, Type} = require('@google/genai');
const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');

const typeMapping = {
  'string': Type.STRING,
  'number': Type.NUMBER,
  'integer': Type.INTEGER,
  'boolean': Type.BOOLEAN,
  'array': Type.ARRAY,
  'object': Type.OBJECT,
};

module.exports = {
  name: 'vertex',
  mixins: [FunctionsCommon],
  settings: {},
  hooks: {},

  dependencies: [],
  actions: {
    chatCompletion: {
      timeout: 5 * 60 * 1000,
      rest: 'POST /chatCompletion',
      async handler(ctx) {
        try {
          const {messages, model, schema, responseFormat, responseId, temperature, maxTokens, configKey} = ctx.params;

          const systemInstruction = messages[0].role === 'system'
            ? [{text: messages[0].content}]
            : null;

          const contentMessage = this.convertContent(messages);
          const gemini = new GoogleGenAI({
            ...configKey,
            vertexai: true,
          });
          let body = {
            model: model,
            contents: [...contentMessage],
            temperature,
            config: {
              responseMimeType: 'text/plain',
              systemInstruction,
              maxOutputTokens: maxTokens,
            },
          };

          if (responseFormat === 'json_object') {
            body = {
              ...body,
              config: {
                systemInstruction,
                ...(model.includes('2.5') && {thinkingConfig: {thinkingBudget: 1024}}),
                responseMimeType: 'application/json',
                responseSchema: this.mapSchemaWithArraySupport(schema),
                maxOutputTokens: max_tokens,
              },
              temperature,
            };
          }
          const completion = await gemini.models.generateContent(body);
          this.broker.emit('llmGenerateCompleted', {
            id: responseId,
            completionTokens: completion.usageMetadata.candidatesTokenCount,
            promptTokens: completion.usageMetadata.promptTokenCount,
            totalTokens: completion.usageMetadata.totalTokenCount,
            model,
          });

          return responseFormat === 'json_object' ? JSON.parse(completion.candidates[0].content.parts[0].text)
            : completion.candidates[0].content.parts[0].text;
        } catch (err) {
          console.log(err);
          return err;
        }
      },
    },
    completion: {
      timeout: 5 * 60 * 1000,
      rest: 'POST /completion',
      async handler(ctx) {
        const {model, maxTokens, prompt, configKey} = ctx.params;

        let body = {
          model,
          contents: prompt,
          config: {
            responseMimeType: 'text/plain',
            maxOutputTokens: maxTokens,
          },
        };
        const gemini = new GoogleGenAI(configKey);
        const completion = await gemini.models.generateContent(body);
        return completion.candidates[0].content.parts[0].text;
      },
    },
  },
  events: {},
  methods: {
    async checkSize(filePath) {
      try {
        let stats = fs.statSync(filePath);
        let fileSizeInBytes = stats.size;
        let fileSizeInMegabytes = fileSizeInBytes / (1024 * 1024);
        return fileSizeInMegabytes;
      } catch (error) {
        return error;
      }
    },
    mapSchemaWithArraySupport(schema) {
      if (Array.isArray(schema)) {
        return schema.map(element => this.mapSchemaWithArraySupport(element));
      }

      if (typeof schema !== 'object' || schema === null) {
        return schema;
      }

      const mappedObject = Object.fromEntries(
        Object.entries(schema).map(([key, value]) => {
          const mappedValue = this.mapSchemaWithArraySupport(value);
          return [key, mappedValue];
        }),
      );

      if (mappedObject.hasOwnProperty('type') && typeof mappedObject.type === 'string' && typeMapping.hasOwnProperty(mappedObject.type)) {
        mappedObject.type = typeMapping[mappedObject.type];
      }

      return mappedObject;
    },
    convertContent(messages) {
      return messages
        .filter(message => message.role !== 'system')
        .map(message => {
          const newMessage = {...message};
          newMessage.parts = [{
            text: message.content,
          }];
          delete newMessage.content;
          return newMessage;
        });
    },
  },
  created() {
  },
  started() {
  },
  stopped() {
  },
};
