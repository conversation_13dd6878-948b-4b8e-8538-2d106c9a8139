const fs = require('fs');
const {OpenAI} = require('openai');
const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');

module.exports = {
  name: 'chatgpt',
  mixins: [FunctionsCommon],
  settings: {},
  hooks: {},

  dependencies: [],
  actions: {
    transcriptAudio: {
      timeout: 5 * 60 * 1000,
      rest: 'GET /transcript',
      admin: true,
      async handler(ctx) {
        const {audioPath, configKey} = ctx.params;
        try {
          const sizeCheck = await this.checkSize(audioPath);
          if (sizeCheck > 9.96) {
            return {
              error: 'File size is greater than 10MB, try smaller video',
            };
          }
          const openai = new OpenAI(configKey);
          return await openai.audio.transcriptions.create({
            model: 'whisper-1',
            file: fs.createReadStream(audioPath),
          });
        } catch (err) {
          return err;
        }
      },
    },
    chatCompletion: {
      timeout: 5 * 60 * 1000,
      rest: 'POST /chatCompletion',
      admin: true,
      async handler(ctx) {
        try {
          const {messages, model, schema, responseFormat, responseId, temperature, maxTokens, configKey} = ctx.params;
          const client = new OpenAI(configKey);
          let body = {
            model,
            messages,
            max_tokens: maxTokens,
          };
          if (responseFormat === 'json_object') {
            body = {
              ...body,
              messages,
              tools: [{
                type: 'function',
                function: {
                  name: 'show_response',
                  description: 'Show the response',
                  parameters: schema,
                },
              }],
              tool_choice: {
                type: 'function',
                function: {name: 'show_response'},
              },
              temperature,
            };
          }
          const completion = await client.chat.completions.create(body);
          const {completion_tokens, prompt_tokens, total_tokens} = completion.usage;
          this.broker.emit('llmGenerateCompleted', {
            id: responseId,
            completionTokens: completion_tokens,
            promptTokens: prompt_tokens,
            totalTokens: total_tokens,
            model,
          });
          if (responseFormat === 'json_object') {
            const generatedText = completion.choices[0].message.tool_calls[0].function.arguments;
            return JSON.parse(generatedText);
          }
          return completion.choices[0].message.content;

        } catch (err) {
          console.log(err);
          return err;
        }
      },
    },
    completion: {
      timeout: 5 * 60 * 1000,
      rest: 'POST /completion',
      admin: true,
      async handler(ctx) {
        const {model, maxTokens, prompt, configKey} = ctx.params;

        let body = {
          model,
          prompt,
          max_tokens: maxTokens,
        };
        const client = new OpenAI(configKey);
        const completion = await client.chat.completions.create(body);
        return completion.choices[0].message.content;
      },
    },
    textToSpeech: {
      timeout: 6 * 60 * 1000,
      rest: 'GET /textToSpeech',
      admin: true,
      async handler(ctx) {
        const {text, voice, model = 'tts-1', speed = 1, configKey} = ctx.params;
        try {
          const client = new OpenAI(configKey);
          return await client.audio.speech.create({
            model: model,
            voice: voice || 'alloy',
            speed,
            input: text,
          });
        } catch (err) {
          return err;
        }
      },
    },
  },

  events: {},
  methods: {
    checkSize: async (filePath) => {
      try {
        let stats = fs.statSync(filePath);
        let fileSizeInBytes = stats.size;
        // Convert the file size to megabytes (optional)
        let fileSizeInMegabytes = fileSizeInBytes / (1024 * 1024);
        return fileSizeInMegabytes;
      } catch (error) {
        return error;
      }
    },
  },
  created() {
  },
  async started() {
  },
  async stopped() {
  },
};
