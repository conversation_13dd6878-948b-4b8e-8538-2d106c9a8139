const mongoose = require('mongoose');
const {Schema} = require('mongoose');
const {TOOL, INSTRUCTION, TOOL_GROUP} = require('../../constants/dbCollections');

const schema = new Schema({
  shortName: {type: String, validate: /\S+/},
  urlName: {type: String, validate: /\S+/},
  inputType: {type: String, required: true, validate: /\S+/},
  outputType: {type: String, validate: /\S+/},
  instruction: {type: String},
  visible: {type: String, enum: ['public', 'developing', 'private'], default: 'developing'},
  instructionIds: [{type: Schema.Types.ObjectId, ref: INSTRUCTION}],
  groupToolIds: [{type: Schema.Types.ObjectId, ref: TOOL_GROUP}],
  localization: {
    name: {
      en: {type: String, validate: /\S+/},
      vi: {type: String, validate: /\S+/},
    },
    description: {
      en: {type: String, validate: /\S+/},
      vi: {type: String, validate: /\S+/},
    },
    contentTitle: {
      en: {type: String, validate: /\S+/},
      vi: {type: String, validate: /\S+/},
    },
    categories: {
      en: {type: String, validate: /\S+/},
      vi: {type: String, validate: /\S+/},
    },
    inputPlaceholder: {
      en: {type: String, validate: /\S+/},
      vi: {type: String, validate: /\S+/},
    },
    inputLabel: {
      en: {type: String, validate: /\S+/},
      vi: {type: String, validate: /\S+/},
    },
  },
  isDeleted: {type: Boolean, default: false},
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

module.exports = mongoose.model(TOOL, schema, TOOL);
