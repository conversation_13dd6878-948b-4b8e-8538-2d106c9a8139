const mongoose = require('mongoose');
const {FAVORITE_TOOL} = require('../../../constants/dbCollections');

const schema = new mongoose.Schema({
  userId: {type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true},
  toolId: {type: mongoose.Schema.Types.ObjectId, ref: 'Tool', required: true},
  isDeleted: {type: Boolean, default: false},
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

module.exports = mongoose.model(FAVORITE_TOOL, schema, FAVORITE_TOOL);
