const Model = require('./favoritetools.model');
const DbMongoose = require('../../../mixins/dbMongo.mixin');
const BaseService = require('../../../mixins/baseService.mixin');
const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');
const i18next = require('i18next');

module.exports = {
  name: 'favoritetools',
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon],
  settings: {
    entityValidator: {},
    populates: {},
    populateOptions: [],
  },
  dependencies: [],

  actions: {},
  events: {},
  methods: {},
  created() {
  },
  async started() {
  },
  async stopped() {
  },
  async afterConnected() {
  },
};
