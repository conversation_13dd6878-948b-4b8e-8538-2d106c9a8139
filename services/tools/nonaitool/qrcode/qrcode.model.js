const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const {QR_CODE} = require('../../../../constants/dbCollections');

const {Schema} = mongoose;
const schema = new Schema({
  data: {type: String},
  qrcodeImage: {type: String},
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);

module.exports = mongoose.model(QR_CODE, schema, QR_CODE);
