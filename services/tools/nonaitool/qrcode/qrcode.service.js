const DbMongoose = require('../../../../mixins/dbMongo.mixin');
const MODEL = require('./qrcode.model');
const BaseService = require('../../../../mixins/baseService.mixin');
const AdminService = require('../../../../mixins/adminService.mixin');
const QRCode = require('qrcode');
const i18next = require('i18next');
const {MoleculerClientError} = require('moleculer').Errors;

module.exports = {
  name: 'qrcodes',
  mixins: [DbMongoose(MODEL), BaseService, AdminService],
  settings: {
    populates: {
      'imageId': 'files.get',
    },
    populateOptions: [],
  },

  actions: {
    create: {
      rest: 'POST /',
      admin: false,
      async handler(ctx) {
        const {text} = ctx.params;
        if (!text) {
          throw new MoleculerClientError(i18next.t('error_data_not_found'));
        }

        const qrcode = await this.adapter.findOne({text});
        if (qrcode) return qrcode;

        const options = {
          errorCorrectionLevel: 'H',
          type: 'image/png',
          quality: 0.92,
          margin: 1,
        };
        const image = await QRCode.toDataURL(text, options);
        return this.adapter.insert({data: text, qrcodeImage: image});
      },
    },
  },

  methods: {},

  events: {},
};
