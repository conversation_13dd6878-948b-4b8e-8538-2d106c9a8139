const DbMongoose = require('../../../../mixins/dbMongo.mixin');
const MODEL = require('./shortlink.model'); // Assumes this model has originalUrl and shortCode fields
const BaseService = require('../../../../mixins/baseService.mixin');
const AdminService = require('../../../../mixins/adminService.mixin');
const {MoleculerClientError} = require('moleculer').Errors;
const {nanoid} = require('nanoid');
const {getConfig} = require('../../../../config/config');
const config = getConfig(process.env.NODE_ENV);


module.exports = {
  name: 'shortlinks',
  mixins: [DbMongoose(MODEL), BaseService, AdminService],
  settings: {
    populates: {},
    populateOptions: [],
  },

  actions: {
    create: {
      rest: 'POST /',
      params: {
        originalUrl: {type: 'url'},
      },
      admin: false,
      async handler(ctx) {
        const {originalUrl} = ctx.params;

        let shortLink = await this.adapter.findOne({originalUrl});
        if (shortLink) {
          return {
            ...shortLink,
            redirectUrl: this.getRedirectUrl(shortLink.shortCode),
          };
        }

        const shortCode = await this.generateUniqueShortCode();
        shortLink = await this.adapter.insert({originalUrl, shortCode});

        return {
          ...shortLink,
          redirectUrl: this.getRedirectUrl(shortCode),
        };
      },
    },
    check: {
      rest: 'GET /check',
      params: {
        shortCode: {type: 'string'},
      },
      async handler(ctx) {
        const {shortCode} = ctx.params;

        const link = await this.adapter.findOne({shortCode});

        if (!link) {
          throw new MoleculerClientError('Short link not found.', 404, 'NOT_FOUND');
        }

        return `${config.domain}/go/${link.shortCode}`;
      },
    },
  },

  methods: {
    getRedirectUrl(shortCode) {
      return `${config.domain}/go/${shortCode}`;
    },
    async generateUniqueShortCode(maxAttempts = 10) {
      let attempts = 0;

      while (attempts < maxAttempts) {
        const shortCode = nanoid(8);
        const count = await this.adapter.count({query: {shortCode}});

        if (count === 0) {
          return shortCode;
        }

        attempts++;
      }

      throw new MoleculerClientError('Failed to generate unique short code after multiple attempts', 500, 'GENERATION_ERROR');
    },
  },

  events: {},
};
