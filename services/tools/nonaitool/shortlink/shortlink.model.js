const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const {SHORT_LINK} = require('../../../../constants/dbCollections');

const {Schema} = mongoose;
const schema = new Schema({
  originalUrl: {type: String},
  shortCode: {type: String},
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);

module.exports = mongoose.model(SHORT_LINK, schema, SHORT_LINK);
