const carbone = require('carbone');

module.exports = {
  name: 'excels',
  mixins: [],
  settings: {
    populates: {
      'imageId': 'files.get',
    },
    populateOptions: [],
  },

  actions: {},

  methods: {
    async generateDocument(data, templateFilePath) {
      const fileAfterRender = await this.renderDataToFileTemp(templateFilePath, data);
    },
    async generateDocumentByColumns(data, templateFilePath, selectedColumns = null) {
      const fileTemplateFinal = await this.updateFileTempByColumns(templateFilePath, selectedColumns);
      const fileAfterRender = await this.renderDataToFileTemp(fileTemplateFinal, data);
    },
    async renderDataToFileTemp(templateFilePath, data) {
      let opt = {
        renderPrefix: 'bao_cao',
        reportName: 'Báo cáo',
        timezone: 'Asia/Saigon',
      };
      return new Promise((resolve, reject) => {
        carbone.render(templateFilePath, data, opt, function(err, resultFilePath) {
          if (err) return reject(err);
          resolve(resultFilePath);
        });
      });
    },
    async updateFileTempByColumns(templateFilePath, selectedColumns = null) {
      try {
        const workbook = new ExcelJs.Workbook();
        await workbook.xlsx.readFile(templateFilePath);
        const worksheet = workbook.getWorksheet(1); // Lấy sheet đầu tiên

        // Định nghĩa cột và vị trí
        const columnHeaders = worksheet.getRow(1).values; // Lấy tiêu đề cột
        const columnIndexes = {}; // Lưu vị trí của từng cột

        columnHeaders.forEach((header, index) => {
          if (header) columnIndexes[header] = index; // Map tên cột với vị trí
        });

        // Xóa các cột không nằm trong selectedColumns
        Object.keys(columnIndexes)
          .reverse() // Xóa từ phải sang trái để tránh lệch index
          .forEach((col) => {
            if (!selectedColumns.includes(col)) {
              worksheet.spliceColumns(columnIndexes[col], 1);
            }
          });

        // Lưu lại file sau khi xóa cột
        const tempUpdatedTemplate = 'temp_updated_template.xlsx';
        await workbook.xlsx.writeFile(tempUpdatedTemplate);
        return tempUpdatedTemplate;
      } catch (error) {
        reject(error);
      }
    },
  },

  events: {},
};
