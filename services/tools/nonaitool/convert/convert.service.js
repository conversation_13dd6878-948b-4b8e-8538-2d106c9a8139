module.exports = {
  name: 'converts',
  mixins: [],
  settings: {
    populates: {},
    populateOptions: [],
  },

  actions: {
    create: {
      rest: 'POST /',
      // auth: "required",
      async handler(ctx) {
        const {fileId, type} = ctx.params;
        const file = ctx.call('files.get', {id: fileId});

        console.log(image);
        return 'a';
      },
    },
    audio: {
      // rest: "POST /audio",
      async handler(ctx) {
      },
    },

    videos: {
      rest: 'POST /video',
      async handler(ctx) {
      },
    },
  },

  methods: {},

  events: {},
};
