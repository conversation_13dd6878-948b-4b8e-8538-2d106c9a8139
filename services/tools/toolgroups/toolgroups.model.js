const mongoose = require('mongoose');
const {Schema} = require('mongoose');
const {TOOL_GROUP} = require('../../../constants/dbCollections');

const schema = new Schema({
  localization: {
    groupName: {
      en: {type: String, validate: /\S+/},
      vi: {type: String, validate: /\S+/},
    },
    description: {
      en: {type: String, validate: /\S+/},
      vi: {type: String, validate: /\S+/},
    },
  },
  isDeleted: {type: Boolean, default: false},
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

module.exports = mongoose.model(TOOL_GROUP, schema, TOOL_GROUP);
