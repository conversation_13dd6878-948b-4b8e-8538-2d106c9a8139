const mongoose = require('mongoose');
const {CONTENT, USER, INPUT, TOOL} = require('../../../constants/dbCollections');
const {INPUT_TYPE} = require('../../../constants/constant');

const schema = new mongoose.Schema({
  inputType: {type: String, validate: /\S+/, enum: INPUT_TYPE},
  contentId: {type: mongoose.Schema.Types.ObjectId, ref: CONTENT},
  toolId: {type: mongoose.Schema.Types.ObjectId, ref: TOOL},
  inputData: {type: mongoose.Schema.Types.Mixed},
  isDeleted: {type: Boolean, default: false},
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});
schema.index({contentId: 1});
module.exports = mongoose.model(INPUT, schema, INPUT);
