const DbMongoose = require('../../../mixins/dbMongo.mixin');
const Model = require('./inputs.model');
const BaseService = require('../../../mixins/baseService.mixin');
const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');
const i18next = require('i18next');
const {MoleculerClientError} = require('moleculer').Errors;

module.exports = {
  name: 'inputs',
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon],
  settings: {
    entityValidator: {},
    populates: {},
    populateOptions: [],
  },
  hooks: {},
  actions: {
    getMediaFileName: {
      rest: 'GET /:id/mediaName',
      async handler(ctx) {
        try {
          const {id} = ctx.params;
          const input = await this.adapter.findById(id);
          switch (input.inputType) {
            case 'video':
              if (input.inputData.videoType === 'offline') {
                const offlineVideoInfo = await ctx.call('offlinevideos.get', {id: input.inputData.offlineVideoId});
                return offlineVideoInfo.name?.split('.').slice(0, -1);
              }
              const videoInfo = await ctx.call('videos.videoDetail', {url: input.inputData.url});
              return videoInfo.title;

            case 'audio':
              const fileInfo = await ctx.call('files.get', {id: input.inputData.audioId});
              return fileInfo.displayName?.split('.').slice(0, -1);
            case 'offline_video':
              const offlineVideoInfo = await ctx.call('offlinevideos.get', {id: input.inputData.offlineVideoId});
              return offlineVideoInfo.name?.split('.').slice(0, -1);
            default:
              return null;
          }
        } catch (e) {
          throw new MoleculerClientError(i18next.t('error_input_not_found'), 404);
        }
      },
    },
    remove: {
      rest: 'DELETE /:id',
      async handler(ctx) {
        const {id} = ctx.params;
        const input = await this.adapter.findById(id);
        await this.adapter.updateById(id, {isDeleted: true});
        await ctx.emit('inputDeleted', {inputId: id});
        return input;
      },
    },
  },
  methods: {},
  events: {
    async 'contents.deteted'(payload, sender, event, ctx) {
      await this.adapter.updateMany({contentId: payload._id}, {isDeleted: true});
    },
    async 'contents.deleteMany'(payload, sender, event, ctx) {
      await this.adapter.updateMany({contentId: {$in: payload}}, {isDeleted: true});
    },
    async 'inputsDeleteMany'(payload, sender, event, ctx) {
      await this.adapter.updateMany({_id: payload.inputId}, {isDeleted: true});
    },
  },
};
