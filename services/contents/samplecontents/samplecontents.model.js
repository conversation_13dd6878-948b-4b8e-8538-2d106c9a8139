const mongoose = require('mongoose');
const {SAMPLE_CONTENT, TOOL} = require('../../../constants/dbCollections');

const schema = new mongoose.Schema({
  toolId: {type: mongoose.Schema.Types.ObjectId, ref: TOOL},
  input: {type: mongoose.Schema.Types.Mixed},
  response: {type: mongoose.Schema.Types.Mixed},
  isDeleted: {type: Boolean, default: false},
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

module.exports = mongoose.model(SAMPLE_CONTENT, schema, SAMPLE_CONTENT);
