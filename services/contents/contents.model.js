const mongoose = require('mongoose');
const {CONTENT, TOOL, USER} = require('../../constants/dbCollections');

const schema = new mongoose.Schema({
  name: {type: String, required: true, default: 'New Content'},
  ownerId: {type: mongoose.Schema.Types.ObjectId, ref: USER},
  toolId: {type: mongoose.Schema.Types.ObjectId, ref: TOOL},
  title: {type: mongoose.Schema.Types.Mixed},
  isDeleted: {type: Boolean, default: false},
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

module.exports = mongoose.model(CONTENT, schema, CONTENT);
