const mongoose = require('mongoose');
const {RESPONSE, CONTENT, INPUT, TOOL} = require('../../../constants/dbCollections');

const schema = new mongoose.Schema({
  contentId: {type: mongoose.Schema.Types.ObjectId, ref: CONTENT},
  inputId: {type: mongoose.Schema.Types.ObjectId, ref: INPUT},
  state: {type: String, enum: ['processing', 'done', 'error']},
  output: {type: mongoose.Schema.Types.Mixed},
  outputType: {type: String},
  plaintext: {type: String},
  headline: {type: mongoose.Schema.Types.Mixed},
  lastMessages: {type: mongoose.Schema.Types.Mixed},
  model: {type: String},
  completionTokens: {type: Number},
  promptTokens: {type: Number},
  totalTokens: {type: Number},
  audioDuration: {type: Number},
  examOrder: {type: Number, min: 1},
  canceledSubmit: {type: Boolean, default: false},
  previousOutput: {type: mongoose.Schema.Types.Mixed},
  previousState: {type: String, enum: ['processing', 'done', 'error']},
  isNewResponse: {type: Boolean, default: true},
  isDeleted: {type: Boolean, default: false},
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});
schema.index({contentId: 1});
module.exports = mongoose.model(RESPONSE, schema, RESPONSE);
