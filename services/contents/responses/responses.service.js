const DbMongoose = require('../../../mixins/dbMongo.mixin');
const ResponseModel = require('./responses.model');
const BaseService = require('../../../mixins/baseService.mixin');
const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');
const {MoleculerClientError} = require('moleculer').Errors;
const i18next = require('i18next');
const {OUTPUT_TYPE} = require('../../../constants/constant');
const {Types} = require('mongoose');

module.exports = {
  name: 'responses',
  mixins: [DbMongoose(ResponseModel), BaseService, FunctionsCommon],
  settings: {
    entityValidator: {},
    populates: {'inputId': 'inputs.get'},
    populateOptions: ['inputId'],
  },

  hooks: {
    before: {
      'update|remove': 'checkResponseExist',
    },
    after: {
      find(ctx, res) {
        return res;
      },
    },
  },

  actions: {
    remove: {
      rest: 'DELETE /:id',
      params: {
        id: 'string',
      },
      async handler(ctx) {
        const {id} = ctx.params;
        const {contentId} = await this.adapter.findById(id);

        const allResponse = await this.adapter.find({query: {contentId, isDeleted: false}});

        const currentIndex = allResponse.findIndex(({_id}) => _id.toString() === id.toString());
        if (currentIndex === -1) return;

        const nextIndex = currentIndex + 1;
        const prevIndex = currentIndex - 1;

        const nextResponse = allResponse[nextIndex]?._id;
        const prevResponse = allResponse[prevIndex]?._id;

        const nextId = nextIndex === allResponse.length ? prevResponse : nextResponse;

        await this.adapter.updateById(nextId, {isActivate: true}, {new: true});
        const dataRes = this.adapter.updateById(id, {isDeleted: true, isActivate: false});

        ctx.call('contents.lastUpdatedContent', {id: contentId.toString()});

        return dataRes;
      },
    },
    updateOutput: {
      rest: 'PUT /:id/output',
      params: {
        id: 'string',
      },
      async handler(ctx) {
        const response = ctx.params;
        const {output, id} = response;
        const responseDetail = await ctx.call('responses.get', {id, populate: ['inputId']});
        const {outputType} = responseDetail;

        switch (outputType) {
          case OUTPUT_TYPE.HTML:
            output.text = this.convertHTMLToText(output?.html);
            output.markdown = this.convertHTMLToText(output?.html);
            break;
          case OUTPUT_TYPE.HTML_QUESTIONS:
            output.questionsText = this.convertHTMLToText(output?.questionsHtml);
            output?.answersHtml && (output.answersText = this.convertHTMLToText(output?.answersHtml));
            break;
          default:
            break;
        }
        const plaintext = await this.broker.call('tools.plaintextFromTool', {outputType, output});
        return this.adapter.updateById(id, {...response, plaintext});
      },
    },
    retrySubmit: {
      rest: 'PUT /:id/retrySubmit',
      timeout: 2 * 60 * 1000,
      params: {},
      async handler(ctx) {
        const {id, additionalRequest, workspaceId, inputData, projectId} = ctx.params;

        const responses = await ctx.call('responses.get', {id, populate: ['inputId']});

        if (additionalRequest) {
          responses.inputId.inputData.additionalRequest = additionalRequest;
        }
        const {_id: inputId} = responses.inputId;
        const {contentId, toolId} = responses;

        let inputObject = responses.inputId;
        const isExamProject = responses.inputId?.inputData?.examOptions;

        if (inputData) {
          let inputUpdate = {
            id: responses.inputId._id,
            inputData: {
              ...inputData,
              commonOptions: responses.inputId?.inputData?.commonOptions,
              examOptions: responses.inputId?.inputData?.examOptions,
            },
            $inc: {numberSubmit: 1},
          };
          const inputDetail = await ctx.call('inputs.update', inputUpdate);
          inputObject = {
            inputData: {...inputDetail.inputData, additionalRequest},
            inputType: inputDetail.inputType,
          };
        }
        if (projectId) {
          const projects = await ctx.call('projects.get', {id: projectId});
          if (['MARK_TEST_IELTS', 'MARK_TEST_SCHOOL'].includes(projects.type)) {
            inputObject.inputData = {
              ...inputObject.inputData,
              ...projects.commonOptions,
            };
          }
        }
        const processingResponse = {
          id,
          inputId,
          contentId,
          toolId,
          output: {
            text: 'Hold on! We are processing',
          },
          state: 'processing',
          isActivate: true,
          rating: undefined,
          previousState: responses.state,
          previousOutput: responses.output,
          canceledSubmit: false,
        };
        processingResponse.numberSubmit++;
        const savedResponse = await ctx.call('responses.updateStreamResponse', processingResponse);
        inputObject.inputData.isExamProject = isExamProject;
        ctx.call('responses.deactivate', {id, contentId});
        ctx.call('contents.submitInput', {input: inputObject, processingResponse: savedResponse, projectId});
        ctx.emit('userSubmited', {inputType: responses.inputId.inputType, workspaceId});
        if (projectId) {
          ctx.emit('listenProject', {projectId, responses: [savedResponse]});
        }
        return savedResponse;
      },
    },
    regenerate: {
      rest: 'PUT /:id/regenerate',
      params: {},
      async handler(ctx) {
        let {id, additionalRequest, workspaceId} = ctx.params;
        const responses = await ctx.call('responses.get', {id, populate: ['inputId']});
        additionalRequest = 'the number gaps is 7';
        const {_id: inputId} = responses.inputId;
        const {contentId} = responses;

        const processingResponse = {
          id,
          inputId,
          contentId,
          output: {
            text: 'Hold on! We are processing',
          },
          state: 'processing',
          isActivate: true,
        };

        const savedResponse = await ctx.call('responses.updateStreamResponse', processingResponse);
        responses.inputId.inputData.additionalRequest = additionalRequest;
        ctx.call('responses.deactivate', {id, contentId});
        ctx.call('contents.submitInput', {input: responses.inputId, processingResponse: savedResponse});
        ctx.emit('userSubmited', {inputType: responses.inputId.inputType, workspaceId});
        return savedResponse;
      },
    },
    updateStreamResponse: {
      timeout: 60 * 1000,
      rest: 'PUT /:id/updateStreamResponse',
      params: {
        id: 'string',
      },
      async handler(ctx) {
        const entity = ctx.params;
        entity.isNewResponse = false;
        let currentResponse;
        if (entity.state !== 'processing') {
          const oldResponse = await this.adapter.findById(entity.id);
          if (oldResponse.canceledSubmit) {
            return await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, currentResponse);
          }
        }
        if ((entity.output.tag || entity.output.category) && entity.projectId) {
          this.broker.call('projects.update', {
            id: entity.projectId,
            tag: [entity.output.tag],
            category: [entity.output.category],
          });
        }
        currentResponse = await this.adapter.updateById(entity.id, entity);
        return await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, currentResponse);
      },
    },
    update: {
      rest: 'PUT /:id',
      params: {
        id: 'string',
      },
      async handler(ctx) {
        const {rating, id} = ctx.params;
        const currentResponse = await this.adapter.findById(id);
        if (rating && !currentResponse.rating) {
          const response = await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, currentResponse);
          ctx.emit('createConversation', {response, rating});
        }
      },
    },
    cancelRating: {
      rest: 'PUT /:id/cancelRating',
      params: {
        id: 'string',
      },
      async handler(ctx) {
        const {id} = ctx.params;
        return this.adapter.updateById(id, {rating: undefined});
      },
    },
    deleteMany: {
      rest: 'DELETE /deleteMany',
      async handler(ctx) {
        const {query} = ctx.params;
        if (!query) return [];

        const {ids, projectId} = JSON.parse(query);
        if (!Array.isArray(ids) || ids.length === 0) return [];

        const response = await this.adapter.find({query: {_id: {$in: ids}}});
        if (!response.length) return [];

        const contentIds = response.map(res => res.contentId);
        const {examOrder} = response[0];
        const projects = await ctx.call('projects.get', {id: projectId});

        await Promise.all([
          this.adapter.updateMany(
            {contentId: {$in: contentIds}, examOrder: {$gt: examOrder}, isDeleted: false},
            {$inc: {examOrder: -1}},
          ),
          this.adapter.updateMany({_id: {$in: ids}}, {isDeleted: true}),
          ctx.call('projects.update', {
            id: projectId,
            numberOfExams: Math.max(0, projects.numberOfExams - 1),
          }),
        ]);

        await ctx.emit('inputsDeleteMany', {inputId: {$in: response.map(res => res.inputId)}});

        return response;
      },
    },
    getOneByInputId: {
      rest: 'GET /:inputId/getOne',
      async handler(ctx) {
        const {inputId} = ctx.params;
        return await this.adapter.findOne({inputId, isDeleted: false});

      },
    },
    deleteMarkTestResponse: {
      rest: 'DELETE /:id/deleteMarkTestResponse',
      async handler(ctx) {
        const {id} = ctx.params;
        const response = await this.adapter.updateById(id, {isDeleted: true});
        await ctx.call('inputs.update', {id: response.inputId.toString(), isHasResponse: false});
        return response;
      },
    },
    cancelSubmit: {
      rest: 'PUT /:id/cancelSubmit',
      async handler(ctx) {
        const {id} = ctx.params;
        const response = await this.adapter.findById(id);
        const responseUpdated = await this.adapter.updateById(id, {
          canceledSubmit: true,
          state: response.previousState,
          output: response.previousOutput,
        });
        await this.broker.emit(`sse.${id}`, responseUpdated);
        return responseUpdated;
      },
    },
  },
  methods: {
    async checkResponseExist(ctx) {
      const {id} = ctx.params;
      const response = await this.adapter.findById({_id: id});
      if (response.isDeleted) {
        throw new MoleculerClientError(i18next.t('response_was_deleted'), 404);
      }
    },
  },
  events: {
    llmGenerateCompleted: {
      async handler(context) {
        const entity = context.params;
        await this.adapter.updateById(entity.id, entity);
      },
    },
    listenResponse: {
      params: {
        responseId: 'string',
      },
      async handler(context) {
        const {responseId} = context.params;
        const response = await this.adapter.findById(responseId);
        context.emit(`sse.${responseId}`, response);
      },
    },
    inputDeleted: {
      params: {
        inputId: 'string',
      },
      async handler(context) {
        const {inputId} = context.params;
        await this.adapter.updateMany({inputId}, {isDeleted: true});
      },
    },
    async 'contents.deteted'(payload, sender, event, ctx) {
      this.logger.info('payload', payload, sender, event);

      const project = await this.broker.call('projects.get', {id: payload.projectId?.toString()});

      if (project.type === 'EXAM_SCHOOL') {
        const allContents = await this.broker.call('contents.find', {
          query: {
            projectId: payload.projectId,
            isDeleted: false,
          },
        });
        const allResponses = await this.broker.call('responses.find', {
          query: {
            contentId: {$in: allContents.map(content => content._id)},
            isDeleted: false,
          },
        });

        let maxExamOrder = 0;
        const mapResponse = allResponses.reduce((map, response) => {
          map[response.contentId] = (map[response.contentId] || 0) + 1;
          if (map[response.contentId] > maxExamOrder) {
            maxExamOrder = map[response.contentId];
          }
          return map;
        }, {});

        const keysSorted = Object.values(mapResponse).sort((a, b) => b - a);
        if (keysSorted.length === 1) {
          await ctx.call('projects.update', {id: payload.projectId, numberOfExams: 0});
        } else if (mapResponse[payload._id] === maxExamOrder && keysSorted[1] !== maxExamOrder) {
          await this.adapter.updateMany(
            {$and: [{contentId: {$ne: payload._id}}, {contentId: {$in: allContents.map(content => content._id)}}]},
            {$inc: {examOrder: -(maxExamOrder - keysSorted[1])}},
          );
          await ctx.call('projects.update', {
            id: payload.projectId,
            numberOfExams: keysSorted[1],
            activeExam: keysSorted[1],
          });
        }
      }

      return this.adapter.updateMany({contentId: payload._id}, {isDeleted: true});
    },
    async 'contents.deleteMany'(payload, sender, event, ctx) {
      this.logger.info('payload', payload, sender, event);

      return this.adapter.updateMany({contentId: {$in: payload}}, {isDeleted: true});
    },
    responseCheckProcessing: {
      async handler(context) {
        await this.adapter.updateMany(
          {
            isDeleted: false,
            state: 'processing',
            updatedAt: {$lte: new Date(Date.now() - 2 * 60 * 1000)},
          },
          {
            $set: {
              state: 'error',
              output: {
                error: {},
                message: 'Response processing timeout',
              },
            },
          },
        );
      },
    },
  },
};
