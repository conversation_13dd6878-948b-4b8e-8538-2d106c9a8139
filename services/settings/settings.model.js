const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const {SETTING} = require('../../constants/dbCollections');

const schema = new mongoose.Schema({
  telegramBotToken: {type: String},
  telegramChatId: {type: String},
  supportEmail: {type: String},
  mailApiKey: {type: String},
  activateTimesWrongPass: {type: Boolean, default: true},
  timesWrongPass: {type: Number, default: 5},
  activateWrongPassToCapcha: {type: Boolean, default: true},
  wrongPassToCapcha: {type: Number, default: 3},
  activeTOTP: {type: Boolean, default: false},
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);

module.exports = mongoose.model(SETTING, schema, SETTING);
