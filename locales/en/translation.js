exports.enTranslation = {
  // Authentication & User Management
  login_fail: 'Invalid username or password',
  error_account_not_active: 'Account is not activated, please check your email to activate your account',
  account_already_exists: 'Account already exists',
  user_email_has_registered: 'Email has already been registered',
  error_user_not_found: 'User not found',
  error_old_password_wrong: 'Old password is incorrect',
  error_unauthorized: 'Unauthorized access',
  google_email_not_verified: 'Google email is not verified',
  activation_account_has_expired: 'Account activation has expired',
  account_has_been_activated: 'Account has already been activated',

  // Email subjects and content
  email_subject_user_forgot_password: 'Forgot Password',
  email_subject_user_register: 'Account Registration Successful',
  email_user_create_from: 'Clickee AI',
  email_user_create_subject: 'Account Created Successfully',
  email_user_create_html1: 'You have successfully created an account in Clickee.ai System, Account Information',
  email_user_register_html1: 'You have successfully registered an account in Clickee.ai System, Account Information',
  email_user_update_html1: 'Your account has been updated in Clickee.ai, Account Information',
  check_email_for_reset_link: 'Please check your email for the password reset link',
  check_email_for_active_account_link: 'Please check your email for the account activation link',
  change_password_successfully: 'Password changed successfully',
  reset_passwo8rd_successfully: 'Password reset successfully',
  error_user_change_message_successful: 'Password Change Notification',

  // 2FA & TOTP
  enter_authenticator_code: 'Please enter the code from Microsoft Authenticator',
  setup_2fa_required: 'The system requires two-factor authentication setup. Please set up Microsoft Authenticator to continue.',

  // Data & Content
  error_data_not_found: 'Data not found',
  error_tool_not_found: 'Tool not found',
  error_example_not_found: 'Example not found',
  error_input_not_found: 'Input not found',
  movie_not_found: 'Movie not found',
  error_delete_sysadmin: 'Cannot delete system administrator',

  // Input validation messages
  no_input_text: 'Please provide input text',
  image_no_text: 'Please upload an image',
  audio_no_text: 'Please upload an audio file',
  video_no_text: 'Please upload a video file',
  file_no_text: 'Please upload a file',
  cannot_evaluate_without_content: 'Cannot evaluate without content',

  // User registration and profile
  error_user_account_is_registered: 'Account has already been registered',
  error_user_email_was_registered: 'Email has already been registered',
  error_user_phone_was_registered: 'Phone number has already been registered',
  error_user_alias_already_exists: 'Alias already exists',

  // System messages
  account_information_updated_successfully: 'Account information updated successfully',
  account_information: 'Account Information',
  full_name: 'Full Name',
  phone: 'Phone',
  email: 'Email',
  sign_in: 'Sign In',
  phone_number_already_exists: 'Phone number already exists',

  // Additional keys found in code
  error_email_not_found: 'Email not found',
  error_google_login: 'Google login error',
};
