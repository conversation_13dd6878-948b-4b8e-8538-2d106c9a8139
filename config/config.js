const config = {
  production: {
    websiteName: 'Demo',
    domain: process.env.DOMAIN || 'https://demo.stealersmile.click',
    client_id: "1056580844777-gvns2idkt1kquld7aut7eks1g0kg9pr9.apps.googleusercontent.com",
    client_secret: "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",
    redirect_uri: `${process.env.DOMAIN || "https://demo.stealersmile.click"}/auth/login-google`,
    backend_base_url: 'http://localhost:3000',
    secret: 'stealersmile',
    resetPasswordSecret: 'reset-password-stealersmile',
    activationSecret: 'activation-stealersmile',
    totpSecret: 'totp-stealersmile',
  },
  development: {
    websiteName: 'Demo',
    domain: process.env.DOMAIN || 'http://localhost:8080',
    client_id: "1056580844777-gvns2idkt1kquld7aut7eks1g0kg9pr9.apps.googleusercontent.com",
    client_secret: "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",
    redirect_uri: "http://localhost:8080/auth/login-google",
    backend_base_url: '',
    secret: 'stealersmile',
    resetPasswordSecret: 'reset-password-stealersmile',
    activationSecret: 'activation-stealersmile',
    totpSecret: 'totp-stealersmile',
  },
};

exports.getConfig = env => config[env] || config.development;
