name: Build and Push Docker Image

concurrency:
  group: "build"
  cancel-in-progress: true

on:
  push:
    tags:
      - '*'
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v2

      - name: Log in to GitLab Container Registry
        run: |
          echo ${{ secrets.GITLAB_TOKEN }} | docker login -u ${{ secrets.GITLAB_USERNAME }} --password-stdin registry.gitlab.com

      - name: Get latest Git tag and Build and push Docker image
        run: |
          git fetch --prune --unshallow
          git checkout master
          git pull origin master
          latest_tag=$(git describe --abbrev=0 --tags 2>/dev/null)
          echo "Building Docker image with tag: $latest_tag"

          TAG=$latest_tag docker compose -f docker-compose-github-action.yml build

          IMAGE_NAME=registry.gitlab.com/${{ secrets.GITLAB_USERNAME }}/demo-api  # Cập nhật tên image nếu cần
          docker tag $IMAGE_NAME:$latest_tag $IMAGE_NAME:latest

          docker push $IMAGE_NAME:$latest_tag
          docker push $IMAGE_NAME:latest

      - name: Logout from GitLab Container Registry
        run: docker logout registry.gitlab.com
