name: Create Git Tag

concurrency:
  group: "build"
  cancel-in-progress: true

on:
  push:
    branches:
      - master

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Setup Git
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "Github Action"

      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v2

      - name: Log in to GitLab Container Registry
        run: |
          echo ${{ secrets.GITLAB_TOKEN }} | docker login -u ${{ secrets.GITLAB_USERNAME }} --password-stdin registry.gitlab.com

      - name: Create new Git tag, Build and push Docker image
        run: |
          # <PERSON><PERSON><PERSON><PERSON> sang nhánh master
          git fetch --prune --unshallow
          git checkout master
          git pull origin master

          # L<PERSON>y tag gần nhất từ git
          latest_tag=$(git describe --abbrev=0 --tags 2>/dev/null || echo "")
          echo "Latest tag: $latest_tag"

          # <PERSON><PERSON><PERSON> không có tag, tạo tag mới bắt đầu từ v1.0.0
          if [ -z "$latest_tag" ]; then
            new_tag="v1.0.0"
          else
            # Tăng phiên bản tag theo format vX.Y.Z
            prefix=$(echo "$latest_tag" | grep -oE '^v[0-9]+\.[0-9]+')
            last_number=$(echo "$latest_tag" | grep -oE '[0-9]+$')
            next_number=$((last_number + 1))
            new_tag="${prefix}.${next_number}"
          fi
          
          # Tạo tag mới và push lên git
          git tag -a "$new_tag" -m "Release $new_tag"
          git push origin "$new_tag"

          echo "New tag: $new_tag"  
          # Build Docker image với tag mới
          TAG=$new_tag docker compose -f docker-compose-github-action.yml build
          IMAGE_NAME=registry.gitlab.com/${{ secrets.GITLAB_USERNAME }}/demo-api 
          docker tag $IMAGE_NAME:$new_tag $IMAGE_NAME:latest

          # Push image lên GitLab Container Registry
          docker push $IMAGE_NAME:$new_tag
          docker push $IMAGE_NAME:latest

      - name: Logout from GitLab Container Registry
        run: docker logout registry.gitlab.com
