name: Notify Telegram on Push

on:
  push:
    branches:
      - main
      - master
      - dev
jobs:
  notify:
    runs-on: ubuntu-latest

    steps:
    - name: Send message to Telegram
      env:
        TELEGRAM_TOKEN: ${{ secrets.TELEGRAM_TOKEN }}
        TELEGRAM_CHAT_ID: ${{ secrets.TELEGRAM_CHAT_ID }}
      run: |
        MESSAGE="User ${{ github.actor }} has pushed to the ${{ github.ref_name }} branch in the repository: $GITHUB_REPOSITORY"
        curl -s -X POST https://api.telegram.org/bot$TELEGRAM_TOKEN/sendMessage -d chat_id=$TELEGRAM_CHAT_ID -d text="$MESSAGE"
