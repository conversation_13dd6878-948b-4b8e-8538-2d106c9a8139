pipeline {
  agent any
  stages {
    stage('Init') {
      steps {
        echo 'Testing..'
        withCredentials([
          string(credentialsId: 'chat_id', variable: 'CHAT_ID'),
          string(credentialsId: 'telegram_token', variable: 'TELEGRAM_TOKEN')
        ]) {
          sh """
            curl -X POST "https://api.telegram.org/bot${TELEGRAM_TOKEN}/sendMessage" \
            -d "chat_id=${CHAT_ID}" \
            -d "text=Building Job - DEMO WEB BACKEND..."
          """
        }
      }
    }
    stage('Build Docker Image and Deploy Stack') {
      steps {
        script {
          sh """
            docker service rm web_api || true
            docker image rm demo-api || true
            docker build -t demo-api:latest ${env.workspace}
            docker stack deploy -c '${env.workspace}/docker-compose.yml' web
            rm -rf ${env.workspace}/*
          """
        }
      }
    }
  }
  post {
    always {
      script {
        withCredentials([
          string(credentialsId: 'chat_id', variable: 'CHAT_ID'),
          string(credentialsId: 'telegram_token', variable: 'TELEGRAM_TOKEN')
        ]) {
          // Gửi thông báo trạng thái cuối cùng
          sh """
            curl -X POST "https://api.telegram.org/bot${TELEGRAM_TOKEN}/sendMessage" \
            -d "chat_id=${CHAT_ID}" \
            -d "text=Build Job - DEMO WEB BACKEND - Build #${BUILD_NUMBER} - STATUS: ${BUILD_STATUS}!"
          """
        }
      }
    }
  }
}
