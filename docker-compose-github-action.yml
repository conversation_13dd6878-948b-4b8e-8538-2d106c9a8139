services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
    image: "registry.gitlab.com/killdate123456/demo-api:${TAG}"
    deploy:
      replicas: 1
      restart_policy:
        condition: any
    environment:
      PORT: 3000
      NODE_ENV: "production"
      MONGO_URI: "mongodb+srv://stearlersmile:<EMAIL>/demo"
    ports:
      - "3010:3000"
    volumes:
      - uploadsfile:/app/services/files/storage

volumes:
  uploadsfile:
    driver: local
