version: "3.8"

services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
      # Use BuildKit for faster builds
      platforms:
        - linux/amd64
        - linux/arm6
    image: "demo-api:latest"
    deploy:
      replicas: 1
      restart_policy:
        condition: any
    environment:
      PORT: 3000
      NODE_ENV: "production"
      MONGO_URI: 'mongodb+srv://stearlersmile:<EMAIL>/demo'
    ports:
      - target: 3000
        published: 3010
        mode: host
    volumes:
      - uploadsfile:/app/services/File/storage

volumes:
  uploadsfile:
    driver: local
