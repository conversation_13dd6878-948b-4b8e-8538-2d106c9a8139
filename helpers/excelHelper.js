const XLSX = require('xlsx');

exports.transformFile = async (stream) => {
  return new Promise((resolve, reject) => {
    const buffers = [];

    stream.on('data', function(data) {
      buffers.push(data);
    });

    stream.on('error', (err) => {
      reject(err);
    });

    stream.on('end', function() {
      try {
        const buffer = Buffer.concat(buffers);
        const workbook = XLSX.read(buffer, {type: 'buffer', cellDates: true, raw: true});

        let sheetData = [];
        workbook.SheetNames.forEach(function(sheetName) {
          let sheet = {
            name: sheetName,
            rows: XLSX.utils.sheet_to_json(workbook.Sheets[sheetName]),
          };
          sheetData.push(sheet);
        });

        resolve(sheetData);
      } catch (err) {
        reject(err);
      }
    });
  });
};

