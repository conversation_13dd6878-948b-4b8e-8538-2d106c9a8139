const { MoleculerClientError } = require('moleculer').Errors;

/**
 * Standardized error codes and messages
 */
const ERROR_CODES = {
  // Authentication errors
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  ACCOUNT_LOCKED: 'ACCOUNT_LOCKED',
  ACCOUNT_INACTIVE: 'ACCOUNT_INACTIVE',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  TOKEN_INVALID: 'TOKEN_INVALID',
  TWO_FACTOR_REQUIRED: 'TWO_FACTOR_REQUIRED',
  INVALID_TWO_FACTOR: 'INVALID_TWO_FACTOR',
  
  // User management errors
  USER_NOT_FOUND: 'USER_NOT_FOUND',
  EMAIL_ALREADY_EXISTS: 'EMAIL_ALREADY_EXISTS',
  EMAIL_NOT_FOUND: 'EMAIL_NOT_FOUND',
  WEAK_PASSWORD: 'WEAK_PASSWORD',
  PASSWORD_REUSE: 'PASSWORD_REUSE',
  
  // Rate limiting
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  
  // Validation errors
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INVALID_INPUT: 'INVALID_INPUT',
  
  // Permission errors
  ACCESS_DENIED: 'ACCESS_DENIED',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  
  // General errors
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE'
};

/**
 * Error messages mapping
 */
const ERROR_MESSAGES = {
  [ERROR_CODES.INVALID_CREDENTIALS]: 'Invalid email or password',
  [ERROR_CODES.ACCOUNT_LOCKED]: 'Account is temporarily locked due to multiple failed login attempts',
  [ERROR_CODES.ACCOUNT_INACTIVE]: 'Account is not active. Please verify your email address',
  [ERROR_CODES.TOKEN_EXPIRED]: 'Token has expired',
  [ERROR_CODES.TOKEN_INVALID]: 'Invalid token',
  [ERROR_CODES.TWO_FACTOR_REQUIRED]: 'Two-factor authentication is required',
  [ERROR_CODES.INVALID_TWO_FACTOR]: 'Invalid two-factor authentication code',
  
  [ERROR_CODES.USER_NOT_FOUND]: 'User not found',
  [ERROR_CODES.EMAIL_ALREADY_EXISTS]: 'Email address is already registered',
  [ERROR_CODES.EMAIL_NOT_FOUND]: 'Email address not found',
  [ERROR_CODES.WEAK_PASSWORD]: 'Password does not meet security requirements',
  [ERROR_CODES.PASSWORD_REUSE]: 'New password must be different from current password',
  
  [ERROR_CODES.RATE_LIMIT_EXCEEDED]: 'Too many requests. Please try again later',
  
  [ERROR_CODES.VALIDATION_ERROR]: 'Validation failed',
  [ERROR_CODES.INVALID_INPUT]: 'Invalid input provided',
  
  [ERROR_CODES.ACCESS_DENIED]: 'Access denied',
  [ERROR_CODES.INSUFFICIENT_PERMISSIONS]: 'Insufficient permissions',
  
  [ERROR_CODES.INTERNAL_ERROR]: 'Internal server error',
  [ERROR_CODES.SERVICE_UNAVAILABLE]: 'Service temporarily unavailable'
};

/**
 * HTTP status codes mapping
 */
const HTTP_STATUS = {
  [ERROR_CODES.INVALID_CREDENTIALS]: 401,
  [ERROR_CODES.ACCOUNT_LOCKED]: 423,
  [ERROR_CODES.ACCOUNT_INACTIVE]: 403,
  [ERROR_CODES.TOKEN_EXPIRED]: 401,
  [ERROR_CODES.TOKEN_INVALID]: 401,
  [ERROR_CODES.TWO_FACTOR_REQUIRED]: 200, // Special case - not an error
  [ERROR_CODES.INVALID_TWO_FACTOR]: 401,
  
  [ERROR_CODES.USER_NOT_FOUND]: 404,
  [ERROR_CODES.EMAIL_ALREADY_EXISTS]: 409,
  [ERROR_CODES.EMAIL_NOT_FOUND]: 404,
  [ERROR_CODES.WEAK_PASSWORD]: 400,
  [ERROR_CODES.PASSWORD_REUSE]: 400,
  
  [ERROR_CODES.RATE_LIMIT_EXCEEDED]: 429,
  
  [ERROR_CODES.VALIDATION_ERROR]: 400,
  [ERROR_CODES.INVALID_INPUT]: 400,
  
  [ERROR_CODES.ACCESS_DENIED]: 403,
  [ERROR_CODES.INSUFFICIENT_PERMISSIONS]: 403,
  
  [ERROR_CODES.INTERNAL_ERROR]: 500,
  [ERROR_CODES.SERVICE_UNAVAILABLE]: 503
};

/**
 * Create standardized error
 */
function createError(code, customMessage = null, data = null) {
  const message = customMessage || ERROR_MESSAGES[code] || 'Unknown error';
  const status = HTTP_STATUS[code] || 500;
  
  return new MoleculerClientError(message, status, code, data);
}

/**
 * Validation error helper
 */
function createValidationError(errors) {
  return createError(
    ERROR_CODES.VALIDATION_ERROR,
    'Validation failed',
    { errors }
  );
}

/**
 * Rate limit error helper
 */
function createRateLimitError(retryAfter = null) {
  return createError(
    ERROR_CODES.RATE_LIMIT_EXCEEDED,
    'Too many requests. Please try again later',
    { retryAfter }
  );
}

/**
 * Account lockout error helper
 */
function createAccountLockError(remainingTime) {
  return createError(
    ERROR_CODES.ACCOUNT_LOCKED,
    `Account is locked. Try again in ${remainingTime} seconds`,
    { remainingTime }
  );
}

/**
 * Password validation error helper
 */
function createPasswordError(errors) {
  return createError(
    ERROR_CODES.WEAK_PASSWORD,
    `Password validation failed: ${errors.join(', ')}`,
    { errors }
  );
}

/**
 * Two-factor required response (not an error)
 */
function createTwoFactorRequired(tempToken) {
  return {
    requiresTwoFactor: true,
    message: 'Two-factor authentication required',
    tempToken,
    code: ERROR_CODES.TWO_FACTOR_REQUIRED
  };
}

/**
 * Success response helper
 */
function createSuccessResponse(message, data = null) {
  const response = {
    success: true,
    message
  };
  
  if (data) {
    response.data = data;
  }
  
  return response;
}

/**
 * Paginated response helper
 */
function createPaginatedResponse(docs, totalDocs, limit, page, totalPages) {
  return {
    docs,
    totalDocs,
    limit,
    page,
    totalPages,
    hasNextPage: page < totalPages,
    hasPrevPage: page > 1,
    nextPage: page < totalPages ? page + 1 : null,
    prevPage: page > 1 ? page - 1 : null
  };
}

module.exports = {
  ERROR_CODES,
  ERROR_MESSAGES,
  HTTP_STATUS,
  createError,
  createValidationError,
  createRateLimitError,
  createAccountLockError,
  createPasswordError,
  createTwoFactorRequired,
  createSuccessResponse,
  createPaginatedResponse
};
