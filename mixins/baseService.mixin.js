const {MoleculerClientError} = require('moleculer').Errors;
const i18next = require('i18next');

module.exports = {
  hooks: {
    before: {
      create(ctx) {
      },
      update(ctx) {
      },
      find(ctx) {
        ctx.params.populate = ctx.params.populate || this.settings.populateOptions;
        ctx.params.query = {...ctx.params.query};
      },
      list(ctx) {
        ctx.params.populate = ctx.params.populate || this.settings.populateOptions;
        const query = ctx.params.query ? JSON.parse(ctx.params.query) : {};
        ctx.params.query = {...query};

        if (ctx.params.searchFields) {
          ctx.params.query = this.convertSearchFields(ctx.params.searchFields, ctx.params.query);
        }
      },
    },
    after: {
      find(ctx, res) {
        return res;
      },
      async create(ctx, res) {
        const data = await this.adapter.findById(res._id);
        return await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, data);
      },
      async update(ctx) {
        const entity = ctx.params;
        let dataExits = await this.adapter.findById(entity.id);
        if (!dataExits) {
          throw new MoleculerClientError(i18next.t('error_data_not_found'), 404);
        }
        const data = await this.adapter.updateById(entity.id, entity);
        return await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, data);
      },
    },
    error: {
      '*': function(ctx, err) {
        console.log('ERROR HOOK++++++', err);
        throw err;
      },
    },
  },
  events: {},
  actions: {
    getAllWithoutPagination: {
      rest: 'GET /findAll',
      async handler(ctx) {
        const {query, populate, searchFields, sort} = ctx.params;
        const parsedQuery = query ? JSON.parse(query) : {};
        const updatedQuery = {...parsedQuery};

        const finalQuery = searchFields ? this.convertSearchFields(searchFields, updatedQuery) : updatedQuery;
        const finalPopulate = populate || this.settings.populateOptions;
        ctx.params = {
          query: finalQuery,
          populate: finalPopulate,
          sort,
        };
        return await ctx.call(`${this.name}.find`, ctx.params);
      },
    },
    updateMany: {
      rest: 'PUT /updateMany',
      async handler(ctx) {
        const {query, update} = ctx.params;
        return await this.adapter.updateMany(query, update);
      },
    },
    deleteMany: {
      rest: 'DELETE /deleteMany',
      async handler(ctx) {
        const {query} = ctx.params;
        const parsedQuery = query ? JSON.parse(query) : {};
        return await this.adapter.removeMany({_id: {$in: parsedQuery.ids}});
      },
    },
  },
  methods: {
    formatRegexString(str) {
      const arrStr = str?.toString()?.split('');
      if (!Array.isArray(arrStr)) return '';
      arrStr.forEach((char, index) => {
        switch (char) {
          case '(':
            arrStr[index] = '\\(';
            break;
          case ')':
            arrStr[index] = '\\)';
            break;
          case '\\':
            arrStr[index] = '\\\\';
            break;
          default:
            break;
        }
      });
      return arrStr.join('');
    },
    convertSearchFields(searchFields, query) {
      const fieldsArray = searchFields.split(',');
      for (let item of fieldsArray) {
        query[item] = new RegExp(this.formatRegexString(query[item]), 'i');
      }
      return query;
    },
  },
};
