const DbService = require('moleculer-db');
const MongooseAdapter = require('moleculer-db-adapter-mongoose');

module.exports = function(mongooseModel) {
  const mongoDbUri =
    process.env.MONGO_URI || 'mongodb+srv://stealersmile:<EMAIL>/tool?retryWrites=true&w=majority&appName=hung';
  // process.env.MONGO_URI || 'mongodb://demo:xuanhung2801@103.152.165.178:27017/tool'

  return {
    mixins: [DbService],
    adapter: new MongooseAdapter(mongoDbUri),
    model: mongooseModel,
    actions: {
      create: {
        visibility: 'published',
      },
      update: {
        visibility: 'published',
      },
      list: {
        visibility: 'published',
      },
      get: {
        visibility: 'published',
      },
      remove: {
        visibility: 'published',
      },
      insertMany: {
        async handler(ctx) {
          return this.adapter.insertMany(ctx.params);
        },
      },
    },
  };
};
