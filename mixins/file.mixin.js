const fs = require('fs');
const path = require('path');
const uploadDir = path.join(__dirname, 'storage');
const axios = require('axios');
const sharp = require('sharp');
const ffmpegPath = require('@ffmpeg-installer/ffmpeg').path;
const ffmpeg = require('fluent-ffmpeg');
ffmpeg.setFfmpegPath(ffmpegPath);

module.exports = {
  methods: {
    createUniqueFileName(fileNameOrigin) {
      let fileName;
      if (fileNameOrigin) {
        let timeStamp = (new Date()).toISOString();
        timeStamp = timeStamp.replace(/:/g, '-');
        fileName = this.appendSuffix(fileNameOrigin.substring(0, 21), `_${timeStamp}`);
      }
      return fileName;
    },
    appendSuffix(fileNameOrigin, suffix) {
      let fileName;
      if (fileNameOrigin) {
        let fileExtension = this.getFileExtension(fileNameOrigin);
        let name = path.parse(fileNameOrigin).name;
        fileName = fileExtension === '' ? `${name}${suffix}` : `${name}${suffix}.${fileExtension}`;
      }
      return fileName;
    },
    getFileExtension(filename) {
      let ext = /^.+\.([^.]+)$/.exec(filename);
      return ext === null ? '' : ext[1];
    },
    getFilePath(fileName = '', filesDir = uploadDir) {
      if (!fileName) return null;
      return path.join(filesDir, fileName);
    },
    createFolderIfNotExist(folderPath) {
      if (!fs.existsSync(folderPath)) {
        fs.mkdirSync(folderPath, {recursive: true});
      }
    },
    getDirPath(dirName = '', rootPath = '') {
      const dirPath = path.resolve(rootPath, dirName);
      this.createFolderIfNotExist(dirPath);
      return dirPath;
    },
    clearFolder(folderPath) {
      if (fs.existsSync(folderPath)) {
        fs.readdirSync(folderPath).forEach((file) => {
          const curPath = `${folderPath}/${file}`;
          if (fs.lstatSync(curPath).isDirectory()) {
            this.clearFolder(curPath);
          } else { // delete file
            fs.unlinkSync(curPath);
          }
        });
        fs.rmdirSync(folderPath);
      }
    },
    saveToLocalStorage(stream, filePath) {
      return new this.Promise((resolve, reject) => {
        //reject(new Error("Disk out of space"));
        const f = fs.createWriteStream(filePath);
        f.on('close', async () => {
          // File written successfully
          this.logger.info(`Uploaded file stored in '${filePath}'`);
          resolve(filePath);
        });

        stream.on('error', (err) => {
          this.logger.info('File error received====================================1', err.message);
          reject(err);
          // Destroy the local file
          f.destroy(err);
        });

        f.on('error', (err) => {
          this.logger.info('File error received====================================2', err.message);
          // Remove the errored file.
          reject(err);
          fs.unlinkSync(filePath);
        });

        stream.pipe(f);
      });
    },
    saveFileToLocal(file, localPath) {
      return new Promise((resolve, reject) => {
        fs.writeFile(localPath, file, function(err) {
          if (err) {
            reject(err);
          }
          resolve(localPath);
        });
      });
    },
    saveVideo(videoUrl, filePath) {
      return new Promise((resolve, reject) => {
        ffmpeg(videoUrl)
          .outputOptions('-c', 'copy')
          .on('error', function(err) {
            console.log('an error happened: ' + err.message);
            reject(err);
          })
          .on('progress', function(progress) {
            console.log('Processing: ' + progress.percent + '% done');
          })
          .on('end', function() {
            resolve(filePath);
          })
          .save(filePath);
      });
    },
    async saveImage(imageUrl, filePath) {
      try {
        const response = await axios.get(imageUrl, {responseType: 'stream'});
        return new Promise(((resolve, reject) => {
          sharp(response.data)
            .resize(325, 525)
            .webp({quality: 80})
            .toFile(filePath)
            .then(() => {
              resolve(filePath);
            })
            .catch(err => {
              reject(err);
            });
        }));
      } catch (e) {
        console.log(e.message);
      }
    },
  },
};
