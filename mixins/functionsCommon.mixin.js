const {camelCase, snakeCase} = require('lodash');
const axios = require('axios');
const fs = require('fs');
const https = require('https');
const {convert} = require('html-to-text');
const QRCode = require('qrcode');
const {marked} = require('marked');
const html2md = require('html-to-md');
const moment = require('moment');
const Ajv = require('ajv');

module.exports = {
  methods: {
    groupBy(listData, key) {
      return listData.reduce(function(grouped, element) {
        (grouped[element[key]] = grouped[element[key]] || []).push(element);
        return grouped;
      }, {});
    },
    extractQueryTime(params) {
      const {time, fromDate, toDate} = params;
      let createdAtQuery = {};

      switch (time) {
        case 'month':
          const {firstDay, lastDay} = this.getMonthRange();
          createdAtQuery = {$gte: firstDay, $lte: lastDay};
          break;
        case 'week':
          const {firstDay: weekFirstDay, lastDay: weekLastDay} = this.getWeekRange();
          createdAtQuery = {$gte: weekFirstDay, $lte: weekLastDay};
          break;
        case 'custom':
          createdAtQuery = {
            $gte: fromDate ? new Date(fromDate * 1000) : new Date(new Date().getFullYear(), new Date().getMonth(), 1),
            $lte: toDate ? new Date(toDate * 1000) : new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0, 23, 59, 59, 999),
          };
          break;
        default:
          const startOfMonth = new Date(new Date().getFullYear(), new Date().getMonth(), 1);
          const endOfMonth = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0, 23, 59, 59, 999);
          createdAtQuery = {$gte: startOfMonth, $lte: endOfMonth};
          break;
      }

      return {createdAt: createdAtQuery};
    },
    extractParamsList(params) {
      const {page, limit, sort} = params;
      return {
        page: +page || 1,
        pageSize: +limit || 12,
        sort: sort || 'createdAt',
      };
    },
    getMonthRange() {
      const date = new Date();
      const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
      const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
      return {firstDay, lastDay};
    },
    getWeekRange() {
      const date = new Date();
      const firstDay = new Date(date.getFullYear(), date.getMonth(), date.getDate() - date.getDay());
      const lastDay = new Date(date.getFullYear(), date.getMonth(), date.getDate() - date.getDay() + 7);
      return {firstDay, lastDay};
    },
    convertSnakeCaseToCamelCase(dataInput) {
      if (typeof dataInput === 'object') {
        if (Array.isArray(dataInput)) {
          let objOutput = [];
          dataInput.forEach(item => {
            objOutput = [...objOutput, this.convertSnakeCaseToCamelCase(item)];
          });
          return objOutput;
        } else {
          return this.convertObjectToCamelCase(dataInput);
        }
      }
      return dataInput;
    },
    convertObjectToCamelCase(objInput) {
      if (!objInput) return objInput;
      const objOutput = {};
      Object.entries(objInput).forEach(([key, value]) => {
        if (key === 'extra') {
          objOutput[key] = value;
        } else {
          if (typeof value === 'object') {
            if (Array.isArray(value)) {
              // array
              objOutput[camelCase(key)] = this.convertSnakeCaseToCamelCase(value);
            } else {
              // object
              objOutput[camelCase(key)] = this.convertObjectToCamelCase(value);
            }
          } else {
            if (key === '_id') {
              objOutput._id = value;
              objOutput.key = value;
            } else {
              objOutput[camelCase(key)] = value;
            }
          }
        }
      });
      return objOutput;
    },
    convertCamelCaseToSnakeCase(dataInput) {
      dataInput = cloneObj(dataInput);
      if (typeof dataInput === 'object') {
        if (Array.isArray(dataInput)) {
          let objOutput = [];
          dataInput.forEach(item => {
            objOutput = [...objOutput, convertCamelCaseToSnakeCase(item)];
          });
          return objOutput;
        } else {
          return convertObjectToSnakeCase(dataInput);
        }
      }
      return dataInput;
    },
    convertObjectToSnakeCase(objInput) {
      if (!objInput) return objInput;
      objInput = cloneObj(objInput);
      const objOutput = {};
      Object.entries(objInput).forEach(([key, value]) => {
        if (key === 'extra' || key === '_id') {
          objOutput[key] = value;
        } else {
          if (typeof value === 'object') {
            if (moment.isMoment(value)) {
              objOutput[snakeCase(key)] = value;
            } else if (Array.isArray(value)) {
              // array
              objOutput[snakeCase(key)] = convertCamelCaseToSnakeCase(value);
            } else {
              // object
              objOutput[snakeCase(key)] = convertObjectToSnakeCase(value);
            }
          } else {
            if (key === '_id') {
              objOutput._id = value;
            } else {
              objOutput[snakeCase(key)] = value !== undefined ? value : null;
            }
          }
        }
      });
      return objOutput;
    },
    async getLinkEpisodes(url) {
      try {
        const response = await axios.get(url);
        const m3u8Url = response.data.match(/const url = "(.*?)";/)[1];
        const domain = new URL(url).origin;
        const link = this.fixUrl(m3u8Url, domain);
        return {link, error: null};
      } catch (e) {
        return {link: url, error: e.message};
      }
    },
    fixUrl(url, domain) {
      if (url.startsWith(domain)) {
        return url;
      }
      return new URL(url, domain).href;
    },
    getValueFromPath(obj, path) {
      const keys = path.split('.').map(key => {
        if (key.startsWith('[') && key.endsWith(']')) {
          return key.slice(1, -1); // Loại bỏ ngoặc vuông cho chỉ mục
        }
        return key;
      });

      return keys.reduce((acc, key) => {
        if (acc === undefined || acc === null) return undefined;
        return acc[key];
      }, obj);
    },
    convertObject(list, key) {
      return list.reduce(function(prevValue, currentValue) {
        prevValue[currentValue?.[key]] = currentValue;
        return prevValue;
      }, {});
    },
    extractIdFromList(listData = []) {
      return listData.map(element => element?._id?.toString());
    },
    extractKeyFromList(listData = [], key) {
      return listData.map(element => element[key]?.toString());
    },
    addIndexToListData(listData = []) {
      return listData?.map((element, index) => {
        element.idx = index + 1;
        return element;
      });
    },
    addStringIndex(listData = []) {
      return listData?.map((element, index) => {
        element.idx = `${index + 1}.  `;
        return element;
      });
    },
    addRomanIndex(listData = []) {
      return listData?.map((element, index) => {
        const romanNumerial = this.convertToRoman(index + 1);
        element.idx = `${romanNumerial}.  `;
        return element;
      });
    },
    secondsToHMS(seconds) {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const remainingSeconds = seconds % 60;

      const formattedHours = String(hours).padStart(2, '0');
      const formattedMinutes = String(minutes).padStart(2, '0');
      const formattedSeconds = String(remainingSeconds).padStart(2, '0');

      return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
    },
    secondsToMS(seconds) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      const formattedMinutes = minutes.toString().padStart(2, '0');
      const formattedSeconds = remainingSeconds.toString().padStart(2, '0');
      return `${formattedMinutes}:${formattedSeconds}`;
    },
    getUniqueObjects(arrayData, key) {
      const uniqueObjectsSet = new Set();
      return arrayData.reduce((result, item) => {
        if (!uniqueObjectsSet.has(item[key])) {
          uniqueObjectsSet.add(item[key]);
          result.push(item);
        }
        return result;
      }, []);
    },
    lineBreak(string) {
      return `${string}\n`;
    },
    convertMarkDownToHTML(string) {
      const options = {
        pedantic: false,
        gfm: true,
        breaks: true,
      };
      const replaceString = string?.replace(/\t/g, '&emsp;');
      let htmlString = marked(replaceString, options);
      htmlString = htmlString
        .replace(/<em>/g, '<i>')
        .replace(/<\/em>/g, '</i>');

      return htmlString;
    },
    convertHTMLToText(html) {
      const options = {
        wordwrap: 130,
      };
      return convert(html, options);
    },
    convertHTMLToMarkdown(html) {
      const options = {
        skipTags: [''],
      };
      const force = {};
      return html2md(html, options, force);
    },
    async imageUrlToBase64(imageUrl) {
      try {
        const response = await axios.get(imageUrl, {responseType: 'arraybuffer'});
        return Buffer.from(response.data, 'binary').toString('base64');
      } catch (error) {
        console.error('Error converting image to base64:', error);
        throw error;
      }
    },
    wrapConversationText(text) {
      return text.replace(/([.?]) /g, '$1\n');
    },
    extractParamsPage(params) {
      const {page, pageSize, sort} = params;
      return {
        page: +page || 1,
        pageSize: +pageSize || 10,
        sort: sort || 'createdAt',
      };
    },
    validateYouTubeUrl(urlToParse) {
      if (urlToParse) {
        const regExp = /^(?:https?:\/\/)?(?:m\.|www\.)?(?:youtu\.be\/|youtube\.com\/(?:embed\/|v\/|watch\?v=|watch\?.+&v=))((\w|-){11})(?:\S+)?$/;
        if (urlToParse.match(regExp)) {
          return true;
        }
      }
      return false;
    },
    getQrCodeImage(url, imagePath) {
      return new Promise((resolve, reject) => {
        QRCode.toFile(imagePath, url, function(err) {
          if (err) {
            reject(err);
          }
          resolve(imagePath);
        });
      });
    },
    saveImageUrlToLocal(imageUrl, localPath) {
      try {
        let download = function(url, dest, cb) {
          let file = fs.createWriteStream(dest);
          let request = https
            .get(url, function(response) {
              response.pipe(file);
              file.on('finish', function() {
                file.close(cb);
              });
            })
            .on('error', function(err) {
              fs.unlink(dest); // Delete the file async if there is an error
              if (cb) cb(err.message);
            });

          request.on('error', function(err) {
            console.log(err);
          });
        };
        return new Promise((resolve, reject) => {
          download(imageUrl, localPath, function(err) {
            if (err) {
              console.log(err);
            } else {
              console.log('Saved image to ' + localPath);
              resolve(localPath);
            }
          });
        });
      } catch (e) {
        console.log(e);
      }
    },
    convertToRoman(num) {
      const roman = [
        {value: 1000, numeral: 'M'},
        {value: 900, numeral: 'CM'},
        {value: 500, numeral: 'D'},
        {value: 400, numeral: 'CD'},
        {value: 100, numeral: 'C'},
        {value: 90, numeral: 'XC'},
        {value: 50, numeral: 'L'},
        {value: 40, numeral: 'XL'},
        {value: 10, numeral: 'X'},
        {value: 9, numeral: 'IX'},
        {value: 5, numeral: 'V'},
        {value: 4, numeral: 'IV'},
        {value: 1, numeral: 'I'},
      ];
      return roman.reduce((acc, {value, numeral}) => {
        const count = Math.floor(num / value);
        num %= value;
        return acc + numeral.repeat(count);
      }, '');
    },
    getUniqueID() {
      return Math.random().toString(36).substring(2, 16);
    },
    splitParagraphsByKeywords(paragraph, keywords) {
      const regex = new RegExp(keywords?.map(k => `(${k})`).join('|'), 'gi');
      return paragraph.split(regex).filter(Boolean);
    },
    replaceKeywords(text, keywords) {
      for (let [key, value] of Object.entries(keywords)) {
        const regex = new RegExp(`\\b${key}\\b`, 'gi');
        text = text.replace(regex, value);
      }
      return text;
    },
    calculateIELTSOverall(listening, reading, writing, speaking) {
      const total = listening + reading + writing + speaking;

      const average = total / 4;

      return Math.round(average * 2) / 2;
    },
    imageTypeFromBase64(base64) {
      const types = {
        'i': 'image/png',
        'I': 'image/png',
        '/': 'image/jpeg',
        'R': 'image/gif',
        'U': 'image/webp',
      };

      return types[base64.charAt(0)] || 'image/jpeg';
    },
    checkValidSchema(schema, data) {
      const ajv = new Ajv();
      return ajv.validate(schema, data);
    },
  },
};
